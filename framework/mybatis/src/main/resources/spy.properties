logMessageFormat=com.p6spy.engine.spy.appender.CustomLineFormat
customLogMessageFormat=\nSQLæå°ï¼%(currentTime)|took %(executionTime)ms|%(category)|connection%(connectionId)\n%(sqlSingleLine)
appender=plus.qdt.common.p6spy.P6SpyLogger
deregisterdrivers=true
useprefix=true
# error,info,batch,debug,statement,commit,rollback,result,resultset.
excludecategories=info,debug,result,commit,resultset
dateformat=yyyy-MM-dd HH:mm:ss.SSS
driverlist=com.mysql.cj.jdbc.Driver
outagedetection=false
outagedetectioninterval=2
filter=true
exclude=information_schema
# exclude: excludeTable