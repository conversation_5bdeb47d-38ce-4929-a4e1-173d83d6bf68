package plus.qdt.mybatis.mybatisplus;

import com.baomidou.mybatisplus.core.conditions.AbstractWrapper;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.util.Map;
import java.util.Properties;

/**
 * 自定义拦截器方法，处理模糊查询中包含特殊字符（_、%、\）
 */
@Intercepts({
        @Signature(type = Executor.class, method = "query", args = { MappedStatement.class, Object.class,
                RowBounds.class, ResultHandler.class }),
        @Signature(type = Executor.class, method = "query", args = { MappedStatement.class, Object.class,
                RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class }) })
public class EscapeInterceptor implements Interceptor {

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 拦截sql

        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        String sqlId = mappedStatement.getId();
        SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();

        Object parameter = invocation.getArgs()[1];
        if (parameter == null || SqlCommandType.SELECT != sqlCommandType) {
            return invocation.proceed();
        }

        if (parameter instanceof MapperMethod.ParamMap && ((MapperMethod.ParamMap<?>) parameter).containsKey("ew")
                && ((MapperMethod.ParamMap) parameter).get("ew") instanceof AbstractWrapper) {
            Map nameValuePairs = ((AbstractWrapper) ((MapperMethod.ParamMap) parameter).get("ew")).getParamNameValuePairs();

            for (Map.Entry<String, Object> next : (Iterable<Map.Entry<String, Object>>) nameValuePairs.entrySet()) {
                if (next.getValue() instanceof String nextStr) {
                    nextStr = nextStr.replace("\\\\", "\\\\\\\\");
                    nextStr = nextStr.replace("%%%", "%\\\\%%");
                    next.setValue((nextStr).trim());
                }
            }
        }
        // 返回
        return invocation.proceed();
    }

    @Override
    public Object plugin(Object target) {
        // TODO Auto-generated method stub
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // TODO Auto-generated method stub
    }
}
