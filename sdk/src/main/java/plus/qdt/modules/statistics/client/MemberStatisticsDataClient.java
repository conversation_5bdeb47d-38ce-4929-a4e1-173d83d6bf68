package plus.qdt.modules.statistics.client;

import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.statistics.entity.dos.MemberStatisticsData;
import plus.qdt.modules.statistics.fallback.MemberStatisticsDataFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @since 2022/1/14
 **/
@FeignClient(name = ServiceConstant.STATISTICS_SERVICE, contextId = "member-statistics-data", fallback = MemberStatisticsDataFallback.class)
public interface MemberStatisticsDataClient {


    @PostMapping("/feign/member-statistics")
    boolean addMemberStatistics(@RequestBody MemberStatisticsData memberStatisticsData);

}
