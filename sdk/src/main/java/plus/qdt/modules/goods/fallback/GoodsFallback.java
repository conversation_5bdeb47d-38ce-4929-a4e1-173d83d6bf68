package plus.qdt.modules.goods.fallback;

import plus.qdt.common.exception.ServiceException;
import plus.qdt.modules.goods.client.GoodsClient;
import plus.qdt.modules.goods.entity.dos.Goods;
import plus.qdt.modules.goods.entity.dos.GoodsSku;
import plus.qdt.modules.goods.entity.dos.Wholesale;
import plus.qdt.modules.goods.entity.dto.GoodsSearchParams;
import plus.qdt.modules.goods.entity.dto.GoodsTopDto;
import plus.qdt.modules.goods.entity.dto.ProxyGoodsOperationDTO;
import plus.qdt.modules.goods.entity.vos.GoodsVO;
import plus.qdt.modules.store.entity.dos.Store;
import plus.qdt.modules.supplier.entity.dto.SupplierGoodsOperationDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * @author: ftyy
 * @date: 2022-01-13 15:29
 * @description: 商品服务Fallback
 */
public class GoodsFallback implements GoodsClient {

    @Override
    public GoodsVO getGoodsVO(String goodsId) {
        throw new ServiceException();
    }

    @Override
    public GoodsSku getGoodsSkuByIdFromCache(String skuId) {
        throw new ServiceException();
    }

    @Override
    public GoodsSku getCanPromotionGoodsSkuByIdFromCache(String skuId) {
        throw new ServiceException();
    }

    @Override
    public void updateStoreDetail(Store store) {
        throw new ServiceException();
    }

    @Override
    public void underStoreGoods(String id) {
        throw new ServiceException();
    }

    @Override
    public Long count(String storeId) {
        throw new ServiceException();
    }

    @Override
    public Integer getStock(String skuId) {
        throw new ServiceException();
    }

    /**
     * 添加商品评价数量
     *
     * @param commentNum 评价数量
     * @param goodsId    商品ID
     */
    @Override
    public void addGoodsCommentNum(Integer commentNum, String goodsId) {
        throw new ServiceException();
    }

    @Override
    public Goods getById(String goodsId) {
        throw new ServiceException();
    }

    @Override
    public List<Goods> queryListByParams(GoodsSearchParams searchParams) {
        throw new ServiceException();
    }

    @Override
    public List<String> getSkuIdsByGoodsId(String goodsId) {
        throw new ServiceException();
    }

    @Override
    public Page<GoodsSku> getGoodsSkuByPage(GoodsSearchParams searchParams) {
        throw new ServiceException();
    }

    @Override
    public List<GoodsSku> getGoodsSkuByList(GoodsSearchParams searchParams) {
        throw new ServiceException();
    }


    @Override
    public void updateGoodsBuyCount(String goodsId, int buyCount) {
        throw new ServiceException();
    }

    @Override
    public GoodsSku getGoodsSkuById(String goodsId) {
        throw new ServiceException();
    }

    @Override
    public void updateGoodsSku(GoodsSku goodsSku) {
        throw new ServiceException();
    }

    @Override
    public void batchUpdateGoodsSku(List<GoodsSku> goodsSkuList) {
        throw new ServiceException();
    }

    @Override
    public Goods getGoodsByParams(GoodsSearchParams searchParams) {
        throw new ServiceException();
    }

    @Override
    public Wholesale getMatchWholesale(String goodsId, Integer num) {
        throw new ServiceException();
    }

    @Override
    public List<Wholesale> getWholesale(String goodsId) {
        throw new ServiceException();
    }

    @Override
    public Long countSkuNum(String storeId) {
        throw new ServiceException();
    }

    @Override
    public void addSupplierGoods(SupplierGoodsOperationDTO supplierGoodsOperationDTO) {
        throw new ServiceException();
    }

    @Override
    public void editSupplierGoods(SupplierGoodsOperationDTO supplierGoodsOperationDTO) {
        throw new ServiceException();
    }

    @Override
    public void batchEditSupplierGoods(List<SupplierGoodsOperationDTO> supplierGoodsOperationDTOList) {
        throw new ServiceException();
    }

    @Override
    public Page<Goods> queryByParams(GoodsSearchParams goodsSearchParams) {
        throw new ServiceException();
    }

    @Override
    public void deleteGoods(List<String> goodsIds) {
        throw new ServiceException();
    }

    @Override
    public void addSupplierGoods(String goodsId) {
        throw new ServiceException();
    }

    @Override
    public void editProxyGoods(ProxyGoodsOperationDTO proxyGoodsOperationDTO) {
        throw new ServiceException();
    }

    @Override
    public void syncStock(List<String> goodsId) {
        throw new ServiceException();
    }

    @Override
    public void syncGoodsSkuCommentCount(String skuId) {
        throw new ServiceException();
    }

    @Override
    public GoodsTopDto goodsTop(String goodsId) {
        throw new ServiceException();
    }
}
