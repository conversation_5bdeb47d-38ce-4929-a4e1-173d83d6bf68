package plus.qdt.modules.goods.client;

import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.goods.entity.dos.Goods;
import plus.qdt.modules.goods.entity.dos.GoodsSku;
import plus.qdt.modules.goods.entity.dos.Wholesale;
import plus.qdt.modules.goods.entity.dto.GoodsSearchParams;
import plus.qdt.modules.goods.entity.dto.GoodsTopDto;
import plus.qdt.modules.goods.entity.dto.ProxyGoodsOperationDTO;
import plus.qdt.modules.goods.entity.vos.GoodsVO;
import plus.qdt.modules.goods.fallback.GoodsFallback;
import plus.qdt.modules.store.entity.dos.Store;
import plus.qdt.modules.supplier.entity.dto.SupplierGoodsOperationDTO;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商品服务client
 *
 * <AUTHOR>
 * @version v1.0 2021-11-08 09:52
 */
@Service
@FeignClient(name = ServiceConstant.GOODS_SERVICE, contextId = "goods",
        fallback = GoodsFallback.class)
public interface GoodsClient {

    /**
     * 添加商品
     *
     * @param supplierGoodsOperationDTO 商品查询条件
     */
    @PostMapping("/feign/supplierGoods/add")
    void addSupplierGoods(@RequestBody SupplierGoodsOperationDTO supplierGoodsOperationDTO);

    /**
     * 修改商品
     *
     * @param supplierGoodsOperationDTO 商品属性
     */
    @PostMapping("/feign/supplierGoods/edit")
    void editSupplierGoods(@RequestBody SupplierGoodsOperationDTO supplierGoodsOperationDTO);

    /**
     * 批量修改商品
     *
     * @param supplierGoodsOperationDTOList 商品属性列表
     */
    @PostMapping("/feign/supplierGoods/batchEdit")
    void batchEditSupplierGoods(@RequestBody List<SupplierGoodsOperationDTO> supplierGoodsOperationDTOList);

    /**
     * 修改代理商品
     *
     * @param proxyGoodsOperationDTO 代理商品编辑参数
     */
    @PostMapping("/feign/goods/proxy/edit")
    void editProxyGoods(@RequestBody ProxyGoodsOperationDTO proxyGoodsOperationDTO);

    /**
     * 获取商品列表
     *
     * @param goodsSearchParams 商品查询条件
     */
    @PostMapping("/feign/goods/list")
    Page<Goods> queryByParams(@RequestBody GoodsSearchParams goodsSearchParams);

    /**
     * 查询商品VO
     *
     * @param goodsId 商品id
     * @return 商品VO
     */
    @GetMapping("/feign/goods/vo/{goodsId}")
    GoodsVO getGoodsVO(@PathVariable String goodsId);

    /**
     * 从缓存中获取商品
     *
     * @param skuId skuid
     * @return 商品详情
     */
    @GetMapping("/feign/goods/sku/{skuId}/cache")
    GoodsSku getGoodsSkuByIdFromCache(@PathVariable String skuId);

    /**
     * 从缓存中获取可参与促销商品
     *
     * @param skuId skuid
     * @return 商品详情
     */
    @GetMapping("/feign/goods/sku/promotion/{skuId}/cache")
    GoodsSku getCanPromotionGoodsSkuByIdFromCache(@PathVariable String skuId);

    /**
     * 批量修改商品信息 以及 sku信息
     *
     * @param store 店铺信息
     */
    @PutMapping("/feign/goods/update/store")
    void updateStoreDetail(@RequestBody Store store);

    /**
     * 批量下架所有店铺商品
     *
     * @param id 店铺id
     */
    @PutMapping("/feign/goods/under/store/{id}")
    void underStoreGoods(@PathVariable String id);

    /**
     * 条件统计商品
     *
     * @param storeId 店铺ID
     * @return 总数
     */
    @GetMapping("/feign/goods/{storeId}/count")
    Long count(@PathVariable String storeId);

    /**
     * 获取sku库存
     *
     * @param skuId skuId
     * @return 库存
     */
    @GetMapping("/feign/goods/sku/{skuId}/stock")
    Integer getStock(@PathVariable String skuId);

    /**
     * 添加商品评价数量
     *
     * @param commentNum 评价数量
     * @param goodsId    商品ID
     */
    @GetMapping("/feign/goods/comment/{goodsId}/num")
    void addGoodsCommentNum(@RequestParam Integer commentNum, @PathVariable String goodsId);

    @GetMapping("/feign/goods/{goodsId}/getById")
    Goods getById(@PathVariable String goodsId);


    @PostMapping("/feign/goods/queryListByParams")
    List<Goods> queryListByParams(@RequestBody GoodsSearchParams searchParams);


    @GetMapping("/feign/goods/{goodsId}/getSkuIdsByGoodsId")
    List<String> getSkuIdsByGoodsId(@PathVariable String goodsId);


    @PostMapping("/feign/goods/sku/page")
    Page<GoodsSku> getGoodsSkuByPage(@RequestBody GoodsSearchParams searchParams);

    @PostMapping("/feign/goods/sku/list")
    List<GoodsSku> getGoodsSkuByList(@RequestBody GoodsSearchParams searchParams);


    @PutMapping("/feign/goods/buy/{goodsId}/count")
    void updateGoodsBuyCount(@PathVariable String goodsId, @RequestParam int buyCount);

    @GetMapping("/feign/goods/sku/{goodsId}")
    GoodsSku getGoodsSkuById(@PathVariable String goodsId);

    @PutMapping("/feign/goods/goods-sku/updateGoodsSku")
    void updateGoodsSku(@RequestBody GoodsSku goodsSku);

    /**
     * 批量更新商品SKU
     *
     * @param goodsSkuList 商品SKU列表
     */
    @PutMapping("/feign/goods/goods-sku/batchUpdateGoodsSku")
    void batchUpdateGoodsSku(@RequestBody List<GoodsSku> goodsSkuList);

    @PostMapping("/feign/goods/getGoodsByParams")
    Goods getGoodsByParams(@RequestBody GoodsSearchParams searchParams);

    /**
     * 匹配批发规则
     *
     * @param goodsId 商品规则
     * @param num     数量
     * @return 批发规则
     */
    @PostMapping("/feign/goods/wholesale/{goodsId}/match")
    Wholesale getMatchWholesale(@PathVariable String goodsId, @RequestParam Integer num);

    /**
     * 获取批发规则
     *
     * @param goodsId 商品规则
     * @return 批发规则
     */
    @PostMapping("/feign/goods/wholesale/query/{goodsId}")
    List<Wholesale> getWholesale(@PathVariable String goodsId);

    /**
     * 统计sku总数
     *
     * @param storeId 店铺id
     * @return sku总数
     */
    @GetMapping("/feign/goods/sku/countSkuNum/{storeId}")
    Long countSkuNum(@PathVariable String storeId);

    /**
     * 商品删除
     *
     * @param goodsIds 批量删除的id集合
     */
    @DeleteMapping("/feign/goods/delete")
    void deleteGoods(@RequestBody List<String> goodsIds);

    /**
     * 店铺代理供应商商品
     *
     * @param goodsId 商品id
     */
    @PutMapping("/feign/goods/proxy")
    void addSupplierGoods(@RequestParam String goodsId);

    /**
     * 店铺代理供应商商品
     *
     * @param goodsId 商品id
     */
    @PutMapping("/feign/goods/syncStock")
    void syncStock(@RequestBody List<String> goodsId);


    @PutMapping("/feign/goods/sku/{skuId}/comment")
    void syncGoodsSkuCommentCount(@PathVariable String skuId);

    /**
     * 根据商品ID获取商品分红、类型和是否需要财通图标
     * @param goodsId 商品ID
     * @return {@link GoodsTopDto}
     * <AUTHOR>
     */
    @GetMapping("/feign/goods/top/{goodsId}")
    GoodsTopDto goodsTop(@PathVariable String goodsId);
}
