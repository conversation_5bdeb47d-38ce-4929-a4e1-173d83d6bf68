package plus.qdt.modules.goods.entity.dos;

import cn.hutool.core.text.CharSequenceUtil;
import plus.qdt.mybatis.model.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 直播间商品
 *
 * <AUTHOR>
 * @since 2021/5/18 5:42 下午
 */
@Data
@Schema(title = "直播间商品")
@TableName("li_studio_commodity")
@NoArgsConstructor
public class StudioCommodity extends BaseEntity {

    private static final long serialVersionUID = 8179291212071954019L;
    @Schema(title = "房间ID")
    private Integer roomId;

    @Schema(title = "商品ID")
    private Integer goodsId;

    public StudioCommodity(Integer roomId, Integer goodsId) {
        this.roomId = roomId;
        this.goodsId = goodsId;
    }

    public LambdaQueryWrapper<StudioCommodity> lambdaQueryWrapper(){
        LambdaQueryWrapper<StudioCommodity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        //id
        if (CharSequenceUtil.isNotEmpty(this.getId())) {
            lambdaQueryWrapper.eq(StudioCommodity::getId, this.getId());
        }
        //房间id
        if (this.roomId!=null && !this.roomId.equals("")) {
            lambdaQueryWrapper.eq(StudioCommodity::getRoomId,this.roomId);
        }
        //商品id
        if(this.goodsId!=null && !this.goodsId.equals("")){
            lambdaQueryWrapper.eq(StudioCommodity::getGoodsId,this.goodsId);
        }

        return lambdaQueryWrapper;
    }
}
