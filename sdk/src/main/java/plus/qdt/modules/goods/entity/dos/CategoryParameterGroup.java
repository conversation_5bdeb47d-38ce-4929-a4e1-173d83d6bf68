package plus.qdt.modules.goods.entity.dos;

import plus.qdt.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

/**
 * 分类参数组关联
 *
 * <AUTHOR>
 * @since 2020-02-26 10:34:02
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("li_category_parameter_group")
@Schema(title = "分类绑定参数组")
public class CategoryParameterGroup extends BaseStandardEntity {

    private static final long serialVersionUID = -3254446505349029420L;

    /**
     * 参数组名称
     */
    @Schema(title = "参数组名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "参数组名称不能为空")
    @Length(max = 20, message = "参数组名称不能超过20字")
    private String groupName;
    /**
     * 关联分类id
     */
    @Schema(title = "关联分类id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "关联的分类不能为空")
    private String categoryId;
    /**
     * 排序
     */
    @Schema(title = "排序", hidden = true)
    private Integer sort;

}