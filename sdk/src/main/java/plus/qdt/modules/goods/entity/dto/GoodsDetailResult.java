package plus.qdt.modules.goods.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import plus.qdt.modules.goods.entity.dto.YZHGoodsSkuDTO;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 商品详情查询结果DTO
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "商品详情查询结果")
public class GoodsDetailResult implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "商品详情列表")
    private List<YZHGoodsSkuDTO> goodsDetailList;

    @Schema(description = "总处理数量")
    private int totalProcessed;

    /**
     * 创建成功结果
     *
     * @param goodsDetailList 商品详情列表
     * @param totalProcessed 总处理数量
     * @return 详情查询结果
     */
    public static GoodsDetailResult success(List<YZHGoodsSkuDTO> goodsDetailList, int totalProcessed) {
        return new GoodsDetailResult(goodsDetailList, totalProcessed);
    }

    /**
     * 创建空结果
     *
     * @return 空的详情查询结果
     */
    public static GoodsDetailResult empty() {
        return new GoodsDetailResult(List.of(), 0);
    }

    /**
     * 获取商品详情数量
     *
     * @return 商品详情数量
     */
    public int getDetailCount() {
        return goodsDetailList != null ? goodsDetailList.size() : 0;
    }

    /**
     * 是否为空
     *
     * @return 是否为空
     */
    public boolean isEmpty() {
        return goodsDetailList == null || goodsDetailList.isEmpty();
    }
}
