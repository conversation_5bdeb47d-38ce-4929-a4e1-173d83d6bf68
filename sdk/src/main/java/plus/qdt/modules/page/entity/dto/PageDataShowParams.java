package plus.qdt.modules.page.entity.dto;

import plus.qdt.common.enums.ClientTypeEnum;
import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.modules.page.entity.enums.PageTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 显示参数获取
 *
 * <AUTHOR>
 * @since 2020/12/10 17:44
 */
@Data
@NoArgsConstructor
public class PageDataShowParams {


    @NotEmpty(message = "页面类型不能为空")
    @Schema(title = "页面类型")
    private PageTypeEnum pageType;

    @NotEmpty(message = "客户端类型不能为空")
    @Schema(title = "客户端类型")
    private ClientTypeEnum pageClientType;

    @NotEmpty(message = "场景不能为空")
    @Schema(title = "场景")
    private SceneEnums useScene;

    @NotEmpty(message = "页面ID不能为空")
    @Schema(title = "扩展ID，平台传递空或者-1，店铺传递店铺id，供应商传递供应商id")
    private String extendId = "-1";

    @Schema(title = "类型为专题时传递专题id")
    private String specialId;

    public SceneEnums getUseScene() {
        if (useScene != null) {
            return useScene;
        }
        return SceneEnums.MEMBER;
    }
}
