package plus.qdt.modules.promotion.entity.dos;

import plus.qdt.common.utils.ValidateParamsUtil;
import plus.qdt.modules.promotion.entity.enums.CouponGetEnum;
import plus.qdt.modules.promotion.entity.enums.CouponRangeDayEnum;
import plus.qdt.modules.promotion.entity.enums.CouponTypeEnum;
import plus.qdt.modules.promotion.entity.enums.PromotionsStatusEnum;
import plus.qdt.modules.promotion.entity.vos.CouponVO;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.beans.BeanUtils;

import java.io.Serial;


/**
 * 优惠券活动实体类
 *
 * <AUTHOR>
 * @since 2020-03-19 10:44 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("li_coupon")
@Schema(title = "优惠券实体类")
@ToString(callSuper = true)
@NoArgsConstructor
public class Coupon extends BaseStandardPromotions {

    @Serial
    private static final long serialVersionUID = 8372820376262437018L;

    @Schema(title = "优惠券名称")
    private String couponName;

    /**
     * POINT("打折"), PRICE("减免现金");
     *
     * @see plus.qdt.modules.promotion.entity.enums.CouponTypeEnum
     */
    @Schema(title = "优惠券类型")
    private String couponType;

    @Schema(title = "面额")
    private Double price;

    @Schema(title = "折扣")
    private Double couponDiscount;

    /**
     * @see plus.qdt.modules.promotion.entity.enums.CouponGetEnum
     */
    @Schema(title = "优惠券类型，分为免费领取和活动赠送")
    private String getType;

    @Schema(title = "店铺承担比例 1~100")
    private Integer storeCommission;

    @Schema(title = "活动描述")
    private String description;

    @Schema(title = "发行数量,如果是0则是不限制")
    private Long publishNum;

    @Schema(title = "领取限制")
    private Long couponLimitNum;

    @Schema(title = "已被使用的数量")
    private Integer usedNum;

    @Schema(title = "已被领取的数量")
    private Integer receivedNum;

    @Schema(title = "消费门槛")
    private Double consumeThreshold;

    /**
     * @see CouponRangeDayEnum
     */
    @Schema(title = "时间范围类型")
    private String rangeDayType;

    @Schema(title = "有效期")
    private Integer effectiveDays;

    public Coupon(CouponVO couponVO) {
        BeanUtils.copyProperties(couponVO, this);
    }


    /**
     * @return 促销状态
     * @see PromotionsStatusEnum
     */
    @Override
    public String getPromotionStatus() {
        if (this.rangeDayType != null && this.rangeDayType.equals(CouponRangeDayEnum.DYNAMICTIME.name())
                && (this.effectiveDays != null && this.effectiveDays > 0 && this.effectiveDays <= 365)) {
            return PromotionsStatusEnum.START.name();
        }
        return super.getPromotionStatus();
    }

    @Override
    public boolean validateParams() {
        if (!ValidateParamsUtil.isValidString(couponName, 2, 50)) {
            ValidateParamsUtil.throwInvalidParamError("优惠券名称长度必须在2-50个字符之间");
        }
        if (!ValidateParamsUtil.isValidEnumValue(couponType, CouponTypeEnum.class)) {
            ValidateParamsUtil.throwInvalidParamError("优惠券类型不正确");
        } else {
            switch (CouponTypeEnum.valueOf(couponType)) {
                case DISCOUNT:
                    if (!ValidateParamsUtil.isValidDoubleValue(couponDiscount, 0, 10)) {
                        ValidateParamsUtil.throwInvalidParamError("优惠券折扣应在0-10之间");
                    }
                    break;
                case PRICE:
                    if (!ValidateParamsUtil.isValidDoubleValue(price, 0, 99999999)) {
                        ValidateParamsUtil.throwInvalidParamError("优惠券面值不正确");
                    }
                    break;
            }
        }

        if (!ValidateParamsUtil.isValidIntValue(storeCommission, 0, 100)) {
            ValidateParamsUtil.throwInvalidParamError("店铺承担比例需在0-100之间");
        }
//        if (!ValidateParamsUtil.isValidString(description, 1, 255)) {
//            ValidateParamsUtil.throwInvalidParamError("优惠券描述不正确");
//        }

        if (!ValidateParamsUtil.isValidEnumValue(getType, CouponGetEnum.class)) {
            ValidateParamsUtil.throwInvalidParamError("优惠券类型不正确");
        } else {
            switch (CouponGetEnum.valueOf(getType)) {
                case FREE:
                    if (!ValidateParamsUtil.isValidLongValue(publishNum, 0, 100000)) {
                        ValidateParamsUtil.throwInvalidParamError("发行数量不超过100000");
                    }
                    if (!ValidateParamsUtil.isValidLongValue(couponLimitNum, 0, 100000)) {
                        ValidateParamsUtil.throwInvalidParamError("领取限制不超过100000");
                    }
                    break;
                case ACTIVITY:
                    // fix: 无需校验有效天数（已经对startTime和endTime做了校验）
                    //if (!ValidateParamsUtil.isValidIntValue(effectiveDays, 1, 365)) {
                    //    ValidateParamsUtil.throwInvalidParamError("有效天数不正确");
                    //}
                    break;
            }
        }

        if (!ValidateParamsUtil.isValidDoubleValue(consumeThreshold, 0, 99999999)) {
            ValidateParamsUtil.throwInvalidParamError("消费门槛应在0-99999999之间");
        }
        if (!ValidateParamsUtil.isValidEnumValue(rangeDayType, CouponRangeDayEnum.class)) {
            ValidateParamsUtil.throwInvalidParamError("时间范围类型不正确");
        }
        return true;
    }

}