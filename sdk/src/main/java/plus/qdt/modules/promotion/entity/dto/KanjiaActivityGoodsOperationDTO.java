package plus.qdt.modules.promotion.entity.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 砍价活动商品操作DTO
 *
 * <AUTHOR>
 * @date 2020/8/21
 **/
@Data
public class KanjiaActivityGoodsOperationDTO implements Serializable {


    @Serial
    private static final long serialVersionUID = -1378599087650538592L;

    @Min(message = "活动开始时间不能为空", value = 0)
    @Schema(title = "活动开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @Min(message = "活动结束时间不能为空", value = 0)
    @Schema(title = "活动结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @Schema(title = "砍价活动商品列表")
    List<KanjiaActivityGoodsDTO> promotionGoodsList;

}
