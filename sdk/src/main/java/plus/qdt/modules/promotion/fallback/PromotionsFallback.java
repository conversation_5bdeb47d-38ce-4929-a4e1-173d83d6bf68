package plus.qdt.modules.promotion.fallback;

import plus.qdt.common.exception.ServiceException;
import plus.qdt.modules.promotion.client.PromotionsClient;
import plus.qdt.modules.promotion.entity.dos.Coupon;
import plus.qdt.modules.promotion.entity.dos.FullDiscount;
import plus.qdt.modules.promotion.entity.dos.MemberCoupon;
import plus.qdt.modules.promotion.entity.dos.PromotionGoods;
import plus.qdt.modules.promotion.entity.dto.search.KanjiaActivitySearchParams;
import plus.qdt.modules.promotion.entity.dto.search.MemberCouponSearchParams;
import plus.qdt.modules.promotion.entity.dto.search.PromotionGoodsSearchParams;
import plus.qdt.modules.promotion.entity.vos.MemberCouponVO;
import plus.qdt.modules.promotion.entity.vos.PintuanVO;
import plus.qdt.modules.promotion.entity.vos.PointsGoodsVO;
import plus.qdt.modules.promotion.entity.vos.kanjia.KanjiaActivityVO;
import plus.qdt.modules.store.entity.dos.Store;

import java.util.List;
import java.util.Map;

/**
 * @author: ftyy
 * @date: 2022-01-17 11:14
 * @description: 促销Fallback
 */
public class PromotionsFallback implements PromotionsClient {
    @Override
    public List<MemberCoupon> getMemberCoupons(MemberCouponSearchParams param) {
        throw new ServiceException();
    }

    @Override
    public List<MemberCouponVO> getMemberCoupons(String memberId) {
        throw new ServiceException();
    }

    @Override
    public FullDiscount getFullDiscount(String id) {
        throw new ServiceException();
    }

    @Override
    public List<MemberCoupon> getAllScopeMemberCoupon(String memberId, List<String> storeId) {
        throw new ServiceException();
    }

    @Override
    public MemberCoupon getMemberCoupon(MemberCouponSearchParams param) {
        throw new ServiceException();
    }

    @Override
    public PointsGoodsVO getPointsGoodsDetailBySkuId(String skuId) {
        throw new ServiceException();
    }

    @Override
    public PintuanVO getPintuanVO(String id) {
        throw new ServiceException();
    }

    @Override
    public KanjiaActivityVO getKanjiaActivityVO(KanjiaActivitySearchParams kanjiaActivitySearchParams) {
        throw new ServiceException();
    }

    @Override
    public List<MemberCoupon> getCurrentGoodsCanUse(String memberId, List<String> couponIds, Double totalPrice) {
        throw new ServiceException();
    }

    @Override
    public List<PromotionGoods> listFindAll(PromotionGoodsSearchParams searchParams) {
        throw new ServiceException();
    }

    @Override
    public PromotionGoods getPromotionsGoods(PromotionGoodsSearchParams searchParams) {
        throw new ServiceException();
    }

    @Override
    public Map<String, Object> getGoodsSkuPromotionMap(String storeId, String goodsSkuId) {
        throw new ServiceException();
    }

    /**
     * 使用优惠券
     *
     * @param couponId 优惠券id
     * @param usedNum  使用数量
     */
    @Override
    public void usedCoupon(String couponId, Integer usedNum) {
        throw new ServiceException();
    }

    @Override
    public void removeByGoodsIds(String goodsIdsJsonStr) {
        throw new ServiceException();
    }

    @Override
    public List<PromotionGoods> findSkuValidPromotions(List<String> skuIds) {
        throw new ServiceException();
    }

    @Override
    public Map<String, Object> wrapperPromotionMapList(List<PromotionGoods> promotionGoodsList) {
        throw new ServiceException();
    }

    @Override
    public Coupon getCouponById(String id) {
        throw new ServiceException();
    }

    @Override
    public void updateStoreInfo(Store store) {
        throw new ServiceException();
    }
}
