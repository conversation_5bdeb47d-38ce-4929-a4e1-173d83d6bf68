package plus.qdt.modules.message.fallback;

import plus.qdt.common.exception.ServiceException;
import plus.qdt.modules.message.client.MessageClient;
import plus.qdt.modules.message.entity.dos.Message;

/**
 * 消息发送
 * 
 * <AUTHOR>
 * @since 2.0
 */
public class MessageFallback implements MessageClient {

    @Override
    public void sendMessage(Message message) {
        throw new ServiceException();
    }
}
