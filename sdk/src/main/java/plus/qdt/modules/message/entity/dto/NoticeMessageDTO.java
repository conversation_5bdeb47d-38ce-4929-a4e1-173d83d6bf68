package plus.qdt.modules.message.entity.dto;

import plus.qdt.modules.message.entity.enums.NoticeMessageNodeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

/**
 * 站内信消息
 * <AUTHOR>
 * @since 2020/12/8 9:46
 */
@Data
public class NoticeMessageDTO {

    @Schema(title = "会员ID")
    private String memberId;

    @Schema(title = "消息节点")
    private NoticeMessageNodeEnum noticeMessageNodeEnum;

    @Schema(title = "消息参数")
    private Map<String,String> parameter;
}
