package plus.qdt.modules.message.entity.vos;

import plus.qdt.modules.message.entity.enums.MessageShowType;
import plus.qdt.modules.message.entity.enums.RangeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 消息
 *
 * <AUTHOR>
 * @since 2020/12/2 17:50
 */
@Data
@Schema(title = "消息")
@AllArgsConstructor
@NoArgsConstructor
public class MessageShowVO {

    private static final long serialVersionUID = 1L;

    @Schema(title = "标题")
    private String title;

    /**
     * @see MessageShowType
     */
    @Schema(title = "消息类型")
    private String type;

    @Schema(title = "消息内容")
    private String content;
    /**
     * @see RangeEnum
     */
    @Schema(title = "发送范围")
    private String messageRange;

}