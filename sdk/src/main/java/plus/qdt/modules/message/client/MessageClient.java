package plus.qdt.modules.message.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import plus.qdt.modules.constant.ServiceConstant;
import plus.qdt.modules.message.entity.dos.Message;
import plus.qdt.modules.message.fallback.MessageFallback;

/**
 * <AUTHOR>
 * @since 2.0
 */
@FeignClient(name = ServiceConstant.SYSTEM_SERVICE, contextId = "message", fallback = MessageFallback.class)
public interface MessageClient {

    /**
     * 发送消息
     * @param message 消息体
     * <AUTHOR>
     */
    @PostMapping("/feign/system/message/send")
    void sendMessage(@RequestBody Message message);

}
