package plus.qdt.modules.payment.entity.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 余额提现记录查询条件
 *
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
@Data
@Schema(title = "余额提现查询条件")
@AllArgsConstructor
@NoArgsConstructor
public class WithdrawApplyQueryVO implements Serializable {


    @Schema(title = "充值订单编号")
    private String sn;

    @Schema(title = "用户Id")
    private String userId;

    @Schema(title = "用户名")
    private String userName;

    /**
     * @see plus.qdt.modules.payment.entity.enums.WithdrawStatusEnum
     */
    @Schema(title = "提现申请状态")
    private String applyStatus;

    @Schema(title = "提现申请时间起始日期")
    private String startDate;

    @Schema(title = "提现申请时间结束日期")
    private String endDate;


}