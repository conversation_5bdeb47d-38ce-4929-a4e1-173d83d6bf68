package plus.qdt.modules.payment.entity.vo;

import plus.qdt.modules.payment.entity.dos.OutOrderDetailLog;
import plus.qdt.modules.payment.entity.dos.OutOrderLog;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 分账记录详情VO
 */
@Data
@NoArgsConstructor
public class OutOrderLogVO implements Serializable {

    @Schema(title = "分账记录")
    private OutOrderLog outOrderLog;

    @Schema(title = "分账记录详情")
    private List<OutOrderDetailLog> outOrderDetailLogList;

    public OutOrderLogVO(OutOrderLog outOrderLog, List<OutOrderDetailLog> outOrderDetailLogs) {
        this.outOrderLog = outOrderLog;
        this.outOrderDetailLogList = outOrderDetailLogs;
    }

}
