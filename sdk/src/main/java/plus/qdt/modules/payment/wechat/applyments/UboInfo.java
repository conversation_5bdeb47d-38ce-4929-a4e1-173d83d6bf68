package plus.qdt.modules.payment.wechat.applyments;

import plus.qdt.modules.payment.entity.SpecEncrypt;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 最终受益人列表
 *
 * <AUTHOR>
 * @since 2022/6/2114:46
 */
@Data
@Schema(title = "最终受益人列表")
public class UboInfo {

    /**
     * 请填写受益人的证件类型。
     * 枚举值：
     * IDENTIFICATION_TYPE_MAINLAND_IDCARD：中国大陆居民-身份证
     * IDENTIFICATION_TYPE_OVERSEA_PASSPORT：其他国家或地区居民-护照
     * IDENTIFICATION_TYPE_HONGKONG：中国香港居民--来往内地通行证
     * IDENTIFICATION_TYPE_MACAO：中国澳门居民--来往内地通行证
     * IDENTIFICATION_TYPE_TAIWAN：中国台湾居民--来往大陆通行证
     * IDENTIFICATION_TYPE_FOREIGN_RESIDENT：外国人居留证
     * IDENTIFICATION_TYPE_HONGKONG_MACAO_RESIDENT：港澳居民证
     * IDENTIFICATION_TYPE_TAIWAN_RESIDENT：台湾居民证
     */
    @Schema(title = "证件类型")
    String ubo_id_doc_type;

    /**
     * 1、请上传受益人证件的正面照片。
     * 2、若证件类型为身份证，请上传人像面照片。
     * 3、可上传1张图片，请填写通过图片上传API预先上传图片生成好的MediaID。
     * 4、请上传彩色照片or彩色扫描件or复印件（需加盖公章鲜章），可添加“微信支付”相关水印（如微信支付认证）。
     */
    @Schema(title = "证件正面照片")
    String ubo_id_doc_copy;

    /**
     * 1、请上传受益人证件的反面照片。
     * 2、若证件类型为身份证，请上传国徽面照片。
     * 3、若证件类型为护照，无需上传反面照片。
     * 4、可上传1张图片，请填写通过图片上传API预先上传图片生成好的MediaID。
     * 5、请上传彩色照片or彩色扫描件or复印件（需加盖公章鲜章），可添加“微信支付”相关水印（如微信支付认证）。
     */
    @Schema(title = "证件反面照片")
    String ubo_id_doc_copy_back;

    /**
     * 该字段需进行加密处理，加密方法详见敏感信息加密说明。(提醒：必须在HTTP头中上送Wechatpay-Serial)
     */
    @Schema(title = "证件姓名")
    @SpecEncrypt
    String ubo_id_doc_name;

    /**
     * 该字段需进行加密处理，加密方法详见敏感信息加密说明。(提醒：必须在HTTP头中上送Wechatpay-Serial)
     */
    @Schema(title = "证件号码")
    @SpecEncrypt
    String ubo_id_doc_number;

    /**
     * 1、请按照证件上住址填写，若证件上无住址则按照实际住址填写，如广东省深圳市南山区xx路xx号xx室。
     * 2、该字段需进行加密处理，加密方法详见敏感信息加密说明。(提醒：必须在HTTP头中上送Wechatpay-Serial)
     */
    @Schema(title = "证件居住地址")
    @SpecEncrypt
    String ubo_id_doc_address;

    /**
     * 1、请按照示例值填写。
     * 2、结束时间大于开始时间。
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "证件有效期开始时间")
    String ubo_id_doc_period_begin;

    /**
     * 1、请按照示例值填写，若证件有效期为长期，请填写：长期。
     * 2、结束时间大于开始时间。
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "证件有效期结束时间")
    String ubo_id_doc_period_end;
}
