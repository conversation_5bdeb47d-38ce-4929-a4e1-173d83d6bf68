package plus.qdt.modules.payment.wechat.model.profitsharing;

import lombok.Data;

/**
 * 分账-接收方回调
 */
@Data
public class ReceiverResponse {
    //分账金额
    private int amount;
    //分账描述
    private String description;
    //分账失败原因
    private String fail_reason;
    // 分账明细单号
    private String detail_id;
    //完成时间
    private String finish_time;
    //分账接收方账号
    private String receiver_account;
    //分账接收商户号
    private String receiver_mchid;
    //分账结果
    private String result;
    //分账接收方类型
    private String type;
}
