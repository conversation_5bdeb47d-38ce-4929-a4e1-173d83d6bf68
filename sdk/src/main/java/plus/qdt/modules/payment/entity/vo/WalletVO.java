package plus.qdt.modules.payment.entity.vo;

import plus.qdt.modules.payment.entity.dos.UserWallet;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 用户当前预存款
 *
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
@Data
@Schema(title = "用户钱包")
@NoArgsConstructor
public class WalletVO {

    @Schema(title = "预存款")
    private Double balance;

    @Schema(title = "预存款冻结金额")
    private Double frozenBalance;

    @Schema(title = "积分")
    private Long points;

    @Schema(title = "企通宝")
    private Double qtbCount;

    public WalletVO(UserWallet userWallet) {
        this.balance = userWallet.getBalance();
        this.frozenBalance = userWallet.getFrozenBalance();
        this.points = userWallet.getPoints();
        this.qtbCount = userWallet.getQtbCount();
    }
}
