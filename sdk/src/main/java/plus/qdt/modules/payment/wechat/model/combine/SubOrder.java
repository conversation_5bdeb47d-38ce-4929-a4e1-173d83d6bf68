package plus.qdt.modules.payment.wechat.model.combine;


import plus.qdt.modules.payment.wechat.model.Amount;
import plus.qdt.modules.payment.wechat.model.SettleInfo;
import lombok.Data;

/**
 * 子订单信息
 *
 * <AUTHOR>
 */
@Data
public class SubOrder {

    /**
     * 子单商户号
     */
    String mchid;

    /**
     * 附加数据
     */
    String attach;

    /**
     * 订单金额
     */
    Amount amount;
    /**
     * 子单商户订单号
     */
    String out_trade_no;

    /**
     * 订单优惠标记
     */
    String goods_tag;

    /**
     * 二级商户号
     * 服务商子商户的商户号，被合单方。直连商户不用传二级商户号
     */
    String sub_mchid;

    /**
     * 商品描述
     */
    String description;

    /**
     * 结算信息
     */
    SettleInfo settle_info;

    /**
     * 子商户应用ID
     */
    String sub_appid;

    /**
     * 微信订单号
     */
    String transaction_id;

    /**
     * 交易状态
     */
    String trade_state;
}
