package plus.qdt.modules.payment.entity.enums;

import lombok.Getter;

/**
 * 支付方式枚举
 *
 * <AUTHOR>
 * @since 2020/12/18 18:08
 */
public enum PaymentMethodEnum {

    /**
     * 微信
     */
    WECHAT("wechatPlugin", "微信", true),
    /**
     * 支付宝
     */
    ALIPAY("aliPayPlugin", "支付宝", true),
    /**
     * 余额支付
     */
    WALLET("walletPlugin", "钱包支付", false),
    /**
     * 财通宝支付
     */
    COIN("coinPlugin", "财通宝支付", false),
    /**
     * 线下转账
     */
    BANK_TRANSFER("bankTransferPlugin", "线下转账", false);

    public static Boolean canReturnOnline(String paymentMethod) {
        return !BANK_TRANSFER.name().equals(paymentMethod);
    }

    /**
     * 插件id 调用对象，需要实现payment接口
     */
    @Getter
    private final String plugin;
    /**
     * 支付名称
     */
    private final String paymentName;

    /**
     * 是否是第三方支付
     */
    private final Boolean isThirdPayment;

    public String paymentName() {
        return paymentName;
    }

    public Boolean getThirdPayment() {
        return isThirdPayment;
    }

    /**
     * 根据插件寻找枚举
     *
     * @param plugin
     * @return
     */
    public static PaymentMethodEnum paymentPluginOf(String plugin) {
        for (PaymentMethodEnum value : PaymentMethodEnum.values()) {
            if (value.plugin.equals(plugin)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据支付方式名称返回对象
     *
     * @param name
     * @return
     */
    public static PaymentMethodEnum paymentNameOf(String name) {
        for (PaymentMethodEnum value : PaymentMethodEnum.values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }

    PaymentMethodEnum(String plugin, String paymentName, Boolean isThirdPayment) {
        this.isThirdPayment = isThirdPayment;
        this.plugin = plugin;
        this.paymentName = paymentName;
    }
}
