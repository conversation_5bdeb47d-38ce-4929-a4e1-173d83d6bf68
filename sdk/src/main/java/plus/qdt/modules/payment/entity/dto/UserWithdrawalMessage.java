package plus.qdt.modules.payment.entity.dto;

import plus.qdt.modules.payment.entity.enums.WithdrawalModeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户提现消息
 *
 * <AUTHOR>
 * @since 2020/12/14 16:31
 */
@Data
public class UserWithdrawalMessage implements Serializable {


    private static final long serialVersionUID = -8449175506402921978L;
    @Schema(title = "用户id")
    private String userId;

    @Schema(title = "金额")
    private Double price;

    @Schema(title = "提现状态")
    private String status;

    @Schema(title = "提现到哪里")
    private WithdrawalModeEnum destination;

}
