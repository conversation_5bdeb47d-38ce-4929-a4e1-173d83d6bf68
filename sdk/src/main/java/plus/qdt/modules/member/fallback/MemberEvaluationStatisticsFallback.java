package plus.qdt.modules.member.fallback;

import plus.qdt.common.exception.ServiceException;
import plus.qdt.modules.member.client.MemberEvaluationStatisticsClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2022/6/24
 **/
public class MemberEvaluationStatisticsFallback implements MemberEvaluationStatisticsClient {
    @Override
    public long todayMemberEvaluation() {
        throw new ServiceException();
    }

    @Override
    public long getWaitReplyNum(String storeId) {
        throw new ServiceException();
    }
}
