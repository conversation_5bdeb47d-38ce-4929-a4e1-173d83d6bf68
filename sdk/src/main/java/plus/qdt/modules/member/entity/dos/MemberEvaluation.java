package plus.qdt.modules.member.entity.dos;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.EqualsAndHashCode;
import plus.qdt.common.enums.SwitchEnum;
import plus.qdt.modules.goods.entity.dos.GoodsSku;
import plus.qdt.modules.member.entity.dto.MemberEvaluationDTO;
import plus.qdt.modules.order.order.entity.dos.Order;
import plus.qdt.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * 会员商品评价
 *
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
@Data
@TableName("li_member_evaluation")
@Schema(title = "会员商品评价")
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MemberEvaluation extends BaseStandardEntity {

    @Schema(title = "会员ID")
    private String memberId;

    @NotNull
    @Schema(title = "店铺ID")
    private String storeId;

    @NotNull
    @Schema(title = "店铺名称")
    private String storeName;

    @NotNull
    @Schema(title = "商品ID")
    private String goodsId;

    @NotNull
    @Schema(title = " SKU ID")
    private String skuId;

    @NotNull
    @Schema(title = "会员名称")
    private String memberName;

    @NotNull
    @Schema(title = "会员头像")
    private String memberProfile;

    @NotNull
    @Schema(title = "商品名称")
    private String goodsName;

    @NotNull
    @Schema(title = "商品图片")
    private String goodsImage;

    @NotNull
    @Schema(title = "订单号")
    private String orderNo;

    @NotNull
    @Schema(title = "好中差评 , GOOD：好评，MODERATE：中评，WORSE：差评", allowableValues = "GOOD,MODERATE,WORSE")
    private String grade;

    @NotNull
    @Schema(title = " 评价内容")
    private String content;

    @Schema(title = "评价图片")
    private String images;

    @NotNull
    @Schema(title = "状态  OPEN 正常 ,CLOSE 关闭 ")
    private String status;

    @Schema(title = "评价回复")
    private String reply;

    @Schema(title = "评价回复图片")
    private String replyImage;

    @Schema(title = "评论是否有图片 true 有 ,false 没有")
    private Boolean haveImage;

    @Schema(title = "回复状态")
    private Boolean replyStatus;

    @Schema(title = "物流评分")
    private Integer deliveryScore;

    @Schema(title = "服务评分")
    private Integer serviceScore;

    @Schema(title = "描述评分")
    private Integer descriptionScore;

    @Schema(title = "是否会员评价")
    private Boolean selfEvaluate;


    public MemberEvaluation(MemberEvaluationDTO memberEvaluationDTO, GoodsSku goodsSku, User user, Order order) {
        //复制评价信息
        BeanUtils.copyProperties(memberEvaluationDTO, this);
        //设置会员
        this.memberId = user.getId();
        //会员名称
        this.memberName = user.getNickName();
        //设置会员头像
        this.memberProfile = user.getFace();
        if (goodsSku != null) {
            //商品名称
            this.goodsName = goodsSku.getGoodsName();
            //商品图片
            this.goodsImage = goodsSku.getThumbnail();
        }
        //设置店铺ID
        this.storeId = order.getStoreId();
        //设置店铺名称
        this.storeName = order.getStoreName();
        //设置订单编号
        this.orderNo = order.getSn();
        //是否包含图片
        this.haveImage = CharSequenceUtil.isNotEmpty(memberEvaluationDTO.getImages());
        //默认开启评价
        this.status = SwitchEnum.OPEN.name();

        this.replyStatus = false;
    }
}