package plus.qdt.modules.member.entity.dos;

import plus.qdt.common.utils.StringUtils;
import plus.qdt.mybatis.model.BaseEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serial;
import java.util.Date;

/**
 * 浏览历史
 *
 * <AUTHOR>
 * @since 2020/11/17 7:22 下午
 */
@Data
@TableName("li_foot_print")
@Schema(title = "浏览历史")
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FootPrint extends BaseEntity {


    @Serial
    private static final long serialVersionUID = -5345119519011397336L;

    @Schema(title = "用户id")
    private String userId;

    @Schema(title = "商品ID")
    private String goodsId;

    @Schema(title = "规格ID")
    private String skuId;

    @Schema(title = "店铺Id")
    private String storeId;

    @CreatedDate
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    @Schema(title = "创建时间", hidden = true)
    private Date createTime;

    public String getGoodsId() {
        return StringUtils.split(goodsId, ",").getFirst();
    }

    public String getSkuId() {
        return StringUtils.split(skuId, ",").getFirst();
    }

}