package plus.qdt.modules.search.entity.dos;

import plus.qdt.common.utils.ValidateParamsUtil;
import plus.qdt.mybatis.model.BaseStandardEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 自定义分词
 *
 * <AUTHOR>
 * @since 2020/10/15
 **/
@Data
@TableName("li_custom_words")
@Schema(title = "自定义分词")
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class CustomWords extends BaseStandardEntity {

    private static final long serialVersionUID = 650889506808657977L;

    /**
     * 名称
     */
    @Schema(title = "名称")
    private String name;


    @Schema(title = "是否禁用: 0,禁用;1,不禁用")
    private Integer disabled;

    public boolean validateParams() {
        if (!ValidateParamsUtil.isValidString(name, 1, 20)) {
            ValidateParamsUtil.throwInvalidParamError("分词长度限制1-20个字符");
        }
        return true;
    }
}
