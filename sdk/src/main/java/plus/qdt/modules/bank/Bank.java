package plus.qdt.modules.bank;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import plus.qdt.common.validation.Mobile;
import plus.qdt.mybatis.model.BaseStandardEntity;

@Data
@TableName("li_user_bank")
@Schema(title = "用户绑定银行卡")
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class Bank extends BaseStandardEntity {

    @NotBlank(message = "银行卡号不能为空")
    @Schema(title = "银行卡号")
    private String bankCode;

    @Schema(title = "开户行")
    private String bankDeposit;

    @NotBlank(message = "所属银行不能为空")
    @Schema(title = "所属银行")
    private String bankName;

    @Mobile
    @NotBlank(message = "银行预留手机号不能为空")
    @Schema(title = "银行预留手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mobile;

    @Schema(title = "类型")
    private String bankType = "储蓄卡";

    @Schema(title = "用户ID")
    private String userId;

    @TableField(exist = false)
    @Schema(title = "开户名")
    private String username;

}
