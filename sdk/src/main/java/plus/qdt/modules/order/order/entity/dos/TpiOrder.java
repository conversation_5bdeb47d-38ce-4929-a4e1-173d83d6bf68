package plus.qdt.modules.order.order.entity.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import plus.qdt.modules.goods.entity.enums.SupplierEnum;
import plus.qdt.mybatis.model.BaseStandardEntity;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 三方订单实体
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@TableName("qdt_tpi_orders")
@Schema(description = "三方订单")
public class TpiOrder extends BaseStandardEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;

    @TableField("system_order_sn")
    @Schema(description = "系统订单号")
    private String systemOrderSn;

    @TableField("third_party_order_id")
    @Schema(description = "三方订单ID")
    private String thirdPartyOrderId;

    @TableField("supplier_type")
    @Schema(description = "供应商类型")
    private String supplierType;

    @TableField("supplier_name")
    @Schema(description = "供应商名称")
    private String supplierName;

    @TableField("member_id")
    @Schema(description = "用户ID")
    private String memberId;

    @TableField("member_name")
    @Schema(description = "用户名称")
    private String memberName;

    @TableField("order_amount")
    @Schema(description = "订单金额")
    private BigDecimal orderAmount;

    @TableField("freight_amount")
    @Schema(description = "运费金额")
    private BigDecimal freightAmount;

    @TableField("total_amount")
    @Schema(description = "订单总金额")
    private BigDecimal totalAmount;

    @TableField("order_status")
    @Schema(description = "订单状态")
    private String orderStatus;

    @TableField("payment_status")
    @Schema(description = "支付状态")
    private String paymentStatus;

    @TableField("delivery_status")
    @Schema(description = "发货状态")
    private String deliveryStatus;

    @TableField("goods_count")
    @Schema(description = "商品数量")
    private Integer goodsCount;

    @TableField("consignee_name")
    @Schema(description = "收货人姓名")
    private String consigneeName;

    @TableField("consignee_mobile")
    @Schema(description = "收货人手机号")
    private String consigneeMobile;

    @TableField("consignee_address")
    @Schema(description = "收货地址")
    private String consigneeAddress;

    @TableField("invoice_flag")
    @Schema(description = "是否开发票")
    private Boolean invoiceFlag;

    @TableField("invoice_title")
    @Schema(description = "发票抬头")
    private String invoiceTitle;

    @TableField("buyer_message")
    @Schema(description = "买家留言")
    private String buyerMessage;

    @TableField("third_party_created_time")
    @Schema(description = "三方订单创建时间")
    private LocalDateTime thirdPartyCreatedTime;

    @TableField("sync_status")
    @Schema(description = "同步状态")
    private String syncStatus;

    @TableField("sync_time")
    @Schema(description = "同步时间")
    private LocalDateTime syncTime;

    @TableField("error_message")
    @Schema(description = "错误信息")
    private String errorMessage;

    @TableField("retry_count")
    @Schema(description = "重试次数")
    private Integer retryCount;

    @TableField("request_data")
    @Schema(description = "请求数据")
    private String requestData;

    @TableField("response_data")
    @Schema(description = "响应数据")
    private String responseData;

    @TableField("remark")
    @Schema(description = "备注")
    private String remark;

    /**
     * 获取供应商枚举
     *
     * @return 供应商枚举
     */
    public SupplierEnum getSupplierEnum() {
        try {
            return SupplierEnum.valueOf(this.supplierType);
        } catch (Exception e) {
            return SupplierEnum.CUSTOM;
        }
    }

    /**
     * 设置供应商枚举
     *
     * @param supplierEnum 供应商枚举
     */
    public void setSupplierEnum(SupplierEnum supplierEnum) {
        this.supplierType = supplierEnum.name();
        this.supplierName = supplierEnum.getDescription();
    }

    /**
     * 是否创建成功
     *
     * @return 是否成功
     */
    public boolean isCreateSuccess() {
        return "SUCCESS".equals(this.syncStatus);
    }

    /**
     * 是否创建失败
     *
     * @return 是否失败
     */
    public boolean isCreateFailed() {
        return "FAILED".equals(this.syncStatus);
    }

    /**
     * 是否处理中
     *
     * @return 是否处理中
     */
    public boolean isProcessing() {
        return "PROCESSING".equals(this.syncStatus);
    }
}
