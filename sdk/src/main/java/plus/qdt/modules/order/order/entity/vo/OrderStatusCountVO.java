package plus.qdt.modules.order.order.entity.vo;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 订单状态数量统计VO
 * 使用 Java 21 Record 特性，提供不可变的数据传输对象
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Schema(description = "订单状态数量统计")
public record OrderStatusCountVO(
        
        @Schema(description = "待付款订单数量", example = "5")
        Long unpaidCount,
        
        @Schema(description = "待发货订单数量", example = "3")
        Long undeliveredCount,
        
        @Schema(description = "待收货订单数量", example = "8")
        Long deliveredCount,
        
        @Schema(description = "待评价订单数量", example = "2")
        Long unfinishedCommentCount,
        
        @Schema(description = "退换货订单数量", example = "1")
        Long afterSaleCount
) {
    
    /**
     * 创建空的统计结果
     *
     * @return 所有数量为0的统计结果
     */
    public static OrderStatusCountVO empty() {
        return new OrderStatusCountVO(0L, 0L, 0L, 0L, 0L);
    }
    
    /**
     * 获取总订单数量
     *
     * @return 所有状态订单的总数量
     */
    public Long getTotalCount() {
        return unpaidCount + undeliveredCount + deliveredCount + unfinishedCommentCount + afterSaleCount;
    }
    
    /**
     * 检查是否有任何订单
     *
     * @return 是否存在订单
     */
    public boolean hasAnyOrders() {
        return getTotalCount() > 0;
    }
}
