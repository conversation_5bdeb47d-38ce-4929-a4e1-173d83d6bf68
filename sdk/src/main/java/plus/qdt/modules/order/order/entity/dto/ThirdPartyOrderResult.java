package plus.qdt.modules.order.order.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import plus.qdt.modules.goods.entity.enums.SupplierEnum;

import java.io.Serial;
import java.io.Serializable;

/**
 * 三方订单创建结果DTO
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "三方订单创建结果")
public class ThirdPartyOrderResult implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "是否成功")
    private boolean success;

    @Schema(description = "供应商类型")
    private SupplierEnum supplier;

    @Schema(description = "三方订单ID")
    private String thirdPartyOrderId;

    @Schema(description = "处理商品数量")
    private int processedCount;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "创建时间")
    private Long createTime;

    /**
     * 创建成功结果
     */
    public static ThirdPartyOrderResult success(SupplierEnum supplier, String thirdPartyOrderId, int processedCount) {
        ThirdPartyOrderResult result = new ThirdPartyOrderResult();
        result.setSuccess(true);
        result.setSupplier(supplier);
        result.setThirdPartyOrderId(thirdPartyOrderId);
        result.setProcessedCount(processedCount);
        result.setCreateTime(System.currentTimeMillis());
        return result;
    }

    /**
     * 创建失败结果
     */
    public static ThirdPartyOrderResult failure(SupplierEnum supplier, String errorMessage) {
        ThirdPartyOrderResult result = new ThirdPartyOrderResult();
        result.setSuccess(false);
        result.setSupplier(supplier);
        result.setProcessedCount(0);
        result.setErrorMessage(errorMessage);
        result.setCreateTime(System.currentTimeMillis());
        return result;
    }
}
