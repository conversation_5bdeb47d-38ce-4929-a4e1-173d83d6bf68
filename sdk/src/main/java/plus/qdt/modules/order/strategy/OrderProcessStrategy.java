package plus.qdt.modules.order.strategy;

import plus.qdt.modules.order.cart.entity.dto.TradeDTO;
import plus.qdt.modules.order.order.entity.dos.Trade;

/**
 * 订单处理策略接口
 * 使用策略模式处理不同类型的订单创建逻辑
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
public interface OrderProcessStrategy {

    /**
     * 处理订单创建
     *
     * @param tradeDTO 交易信息
     * @return 交易结果
     */
    Trade processOrder(TradeDTO tradeDTO);

    /**
     * 是否支持该类型的订单处理
     *
     * @param tradeDTO 交易信息
     * @return 是否支持
     */
    boolean supports(TradeDTO tradeDTO);

    /**
     * 获取策略优先级（数字越小优先级越高）
     *
     * @return 优先级
     */
    int getPriority();
}
