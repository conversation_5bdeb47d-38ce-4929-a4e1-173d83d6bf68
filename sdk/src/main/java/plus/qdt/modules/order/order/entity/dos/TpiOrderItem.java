package plus.qdt.modules.order.order.entity.dos;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import plus.qdt.mybatis.model.BaseStandardEntity;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 三方订单商品明细实体
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Data
@TableName("qdt_tpi_order_items")
@Schema(description = "三方订单商品明细")
public class TpiOrderItem extends BaseStandardEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;

    @TableField("tpi_order_id")
    @Schema(description = "三方订单ID")
    private String tpiOrderId;

    @TableField("system_order_sn")
    @Schema(description = "系统订单号")
    private String systemOrderSn;

    @TableField("third_party_order_id")
    @Schema(description = "三方订单ID")
    private String thirdPartyOrderId;

    @TableField("supplier_type")
    @Schema(description = "供应商类型")
    private String supplierType;

    @TableField("goods_id")
    @Schema(description = "商品ID")
    private String goodsId;

    @TableField("goods_name")
    @Schema(description = "商品名称")
    private String goodsName;

    @TableField("sku_id")
    @Schema(description = "SKU ID")
    private String skuId;

    @TableField("sku_name")
    @Schema(description = "SKU名称")
    private String skuName;

    @TableField("supplier_goods_id")
    @Schema(description = "供应商商品ID")
    private String supplierGoodsId;

    @TableField("supplier_sku_id")
    @Schema(description = "供应商SKU ID")
    private String supplierSkuId;

    @TableField("goods_image")
    @Schema(description = "商品图片")
    private String goodsImage;

    @TableField("specs")
    @Schema(description = "商品规格")
    private String specs;

    @TableField("category_path")
    @Schema(description = "分类路径")
    private String categoryPath;

    @TableField("unit_price")
    @Schema(description = "单价")
    private BigDecimal unitPrice;

    @TableField("quantity")
    @Schema(description = "数量")
    private Integer quantity;

    @TableField("total_price")
    @Schema(description = "总价")
    private BigDecimal totalPrice;

    @TableField("discount_amount")
    @Schema(description = "优惠金额")
    private BigDecimal discountAmount;

    @TableField("actual_amount")
    @Schema(description = "实际金额")
    private BigDecimal actualAmount;

    @TableField("item_status")
    @Schema(description = "商品状态")
    private String itemStatus;

    @TableField("delivery_status")
    @Schema(description = "发货状态")
    private String deliveryStatus;

    @TableField("tracking_number")
    @Schema(description = "快递单号")
    private String trackingNumber;

    @TableField("logistics_company")
    @Schema(description = "物流公司")
    private String logisticsCompany;

    @TableField("remark")
    @Schema(description = "备注")
    private String remark;

    /**
     * 计算总价
     */
    public void calculateTotalPrice() {
        if (this.unitPrice != null && this.quantity != null) {
            this.totalPrice = this.unitPrice.multiply(BigDecimal.valueOf(this.quantity));
        }
    }

    /**
     * 计算实际金额
     */
    public void calculateActualAmount() {
        if (this.totalPrice != null) {
            BigDecimal discount = this.discountAmount != null ? this.discountAmount : BigDecimal.ZERO;
            this.actualAmount = this.totalPrice.subtract(discount);
        }
    }

    /**
     * 是否已发货
     *
     * @return 是否已发货
     */
    public boolean isDelivered() {
        return "DELIVERED".equals(this.deliveryStatus);
    }

    /**
     * 是否已完成
     *
     * @return 是否已完成
     */
    public boolean isCompleted() {
        return "COMPLETED".equals(this.itemStatus);
    }
}
