package plus.qdt.modules.order.order.entity.enums;

/**
 * 三方订单状态枚举
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
public enum TpiOrderStatusEnum {

    /**
     * 待支付
     */
    PENDING_PAYMENT("PENDING_PAYMENT", "待支付"),

    /**
     * 已支付
     */
    PAID("PAID", "已支付"),

    /**
     * 处理中
     */
    PROCESSING("PROCESSING", "处理中"),

    /**
     * 已发货
     */
    SHIPPED("SHIPPED", "已发货"),

    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成"),

    /**
     * 已取消
     */
    CANCELLED("CANCELLED", "已取消"),

    /**
     * 已退款
     */
    REFUNDED("REFUNDED", "已退款"),

    /**
     * 异常
     */
    EXCEPTION("EXCEPTION", "异常");

    private final String code;
    private final String description;

    TpiOrderStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 枚举值
     */
    public static TpiOrderStatusEnum fromCode(String code) {
        for (TpiOrderStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 是否为最终状态
     *
     * @return 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == CANCELLED || this == REFUNDED;
    }

    /**
     * 是否可以取消
     *
     * @return 是否可以取消
     */
    public boolean canCancel() {
        return this == PENDING_PAYMENT || this == PAID || this == PROCESSING;
    }

    /**
     * 是否可以退款
     *
     * @return 是否可以退款
     */
    public boolean canRefund() {
        return this == PAID || this == PROCESSING || this == SHIPPED || this == COMPLETED;
    }
}
