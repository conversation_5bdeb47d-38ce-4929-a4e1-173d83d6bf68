package plus.qdt.modules.order.order.entity.enums;

/**
 * 三方订单同步状态枚举
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
public enum TpiSyncStatusEnum {

    /**
     * 处理中
     */
    PROCESSING("PROCESSING", "处理中"),

    /**
     * 成功
     */
    SUCCESS("SUCCESS", "成功"),

    /**
     * 失败
     */
    FAILED("FAILED", "失败"),

    /**
     * 重试中
     */
    RETRYING("RETRYING", "重试中"),

    /**
     * 已忽略
     */
    IGNORED("IGNORED", "已忽略");

    private final String code;
    private final String description;

    TpiSyncStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取枚举
     *
     * @param code 状态代码
     * @return 枚举值
     */
    public static TpiSyncStatusEnum fromCode(String code) {
        for (TpiSyncStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 是否为最终状态
     *
     * @return 是否为最终状态
     */
    public boolean isFinalStatus() {
        return this == SUCCESS || this == FAILED || this == IGNORED;
    }

    /**
     * 是否可以重试
     *
     * @return 是否可以重试
     */
    public boolean canRetry() {
        return this == FAILED;
    }

    /**
     * 是否需要处理
     *
     * @return 是否需要处理
     */
    public boolean needProcess() {
        return this == PROCESSING || this == RETRYING;
    }
}
