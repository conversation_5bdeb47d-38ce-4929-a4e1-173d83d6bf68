package plus.qdt.modules.purchase.entity.dos;

import plus.qdt.mybatis.model.BaseEntity;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 报价单
 *
 * <AUTHOR>
 * @since 2020/11/26 20:43
 */
@Data
@Schema(title = "供求单报价")
@TableName("li_purchase_quoted")
public class PurchaseQuoted extends BaseEntity {

    @CreatedDate
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    @Schema(title = "创建时间", hidden = true)
    private Date createTime;

    @Schema(title = "采购单ID")
    private String purchaseOrderId;

    @Schema(title = "标题")
    private String title;

    @Schema(title = "报价说明")
    private String context;

    @Schema(title = "附件")
    private String annex;

    @Schema(title = "公司名称")
    private String companyName;

    @Schema(title = "联系人")
    private String contacts;

    @Schema(title = "联系电话")
    private String contactNumber;

    @Schema(title = "报价人")
    private String memberId;

}
