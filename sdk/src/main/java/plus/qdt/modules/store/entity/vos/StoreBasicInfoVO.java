package plus.qdt.modules.store.entity.vos;

import plus.qdt.modules.store.entity.enums.StoreStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 店铺基本信息DTO
 *
 * <AUTHOR>
 * @since 2020/12/7 14:43
 */
@Data
public class StoreBasicInfoVO {

    @Schema(title = "店铺ID")
    private String storeId;

    @Schema(title = "店铺名称")
    private String storeName;

    /**
     * @see StoreStatusEnum
     */
    @Schema(title = "店铺状态")
    private String storeStatus;

    @Schema(title = "地址名称， '，'分割")
    private String companyAddressPath;

    @Schema(title = "店铺logo")
    private String storeLogo;

    @Schema(title = "店铺简介")
    private String storeDesc;

    @Schema(title = "PC端页面")
    private String pcPageData;

    @Schema(title = "移动端页面")
    private String mobilePageData;

    @Schema(title = "是否自营")
    private String selfOperated;

    @Schema(title = "商品数量")
    private Integer goodsNum;

    @Schema(title = "收藏数量")
    private Integer collectionNum;

}
