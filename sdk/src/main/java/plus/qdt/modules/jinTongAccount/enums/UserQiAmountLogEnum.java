package plus.qdt.modules.jinTongAccount.enums;

import lombok.AllArgsConstructor;

import java.util.List;

@AllArgsConstructor
public enum UserQiAmountLogEnum implements AccountType {
    SIGN_ACTIVE(1, "签到-激活到财通宝", BalanceTypeEnum.SUBTRACT),
    GRANT_HAND_OUT(2, "转赠-转给{}", BalanceTypeEnum.SUBTRACT),
    GRANT_PUT(3, "转赠-来自{}", BalanceTypeEnum.ADD),
    GOODS_SUBSIDY(4, "购物消费补贴", BalanceTypeEnum.ADD),
    CONSUMPTION_BONUS(5, "购物消费分红", BalanceTypeEnum.ADD),
    DIRECT_INVITE(6, "邀请奖励-直邀{}", BalanceTypeEnum.ADD),
    INDIRECT_INVITE(7, "邀请奖励-间邀{}", BalanceTypeEnum.ADD),
    DIRECT_SHOP_INVITE(8, "邀请奖励-邀请商家{}入驻", BalanceTypeEnum.ADD),

    ;

    private final int subType;
    private final String describe;
    private final BalanceTypeEnum balanceType;

    public static UserQiAmountLogEnum getEnumByType(Integer type) {
        if (type == null) throw new NullPointerException("type is invalid");
        for (UserQiAmountLogEnum typeEnum : values()) {
            if (typeEnum.subType == type) {
                return typeEnum;
            }
        }
        throw new IllegalArgumentException("type is invalid");
    }

    /**
     * 企通入账类型
     *
     * @return {@link List} {@link Integer}
     * <AUTHOR>
     */
    public static List<Integer> getBonusType() {
        return List.of(GRANT_PUT.subType, GOODS_SUBSIDY.subType, CONSUMPTION_BONUS.subType, DIRECT_INVITE.subType,
                INDIRECT_INVITE.subType, DIRECT_SHOP_INVITE.subType);
    }


    @Override
    public Integer getSubType() {
        return subType;
    }

    @Override
    public String getDescribe() {
        return describe;
    }

    @Override
    public BalanceTypeEnum getBalanceType() {
        return balanceType;
    }
}
