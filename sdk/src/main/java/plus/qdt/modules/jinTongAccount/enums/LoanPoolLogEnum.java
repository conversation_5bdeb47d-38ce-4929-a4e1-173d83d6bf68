package plus.qdt.modules.jinTongAccount.enums;

import lombok.AllArgsConstructor;

/**
 * 借贷池金额流水枚举
 */
@AllArgsConstructor
public enum LoanPoolLogEnum implements AccountType {
    USER_BUY_SELF_BONUS(1, "{}购买自营商品分红", BalanceTypeEnum.SUBTRACT),
    USER_BUY_NOT_SELF(2, "{}购买非自营商品补贴上一级-{}", BalanceTypeEnum.SUBTRACT),
    USER_BUY_NOT_SELF_TWO(3, "{}购买非自营商品补贴上二级-{}", BalanceTypeEnum.SUBTRACT),
    USER_BUY_NOT_SELF_THREE(4, "{}购买非自营商品补贴上三级-{}", BalanceTypeEnum.SUBTRACT),
    USER_INVITE_SHOP(5, "{}邀请商家-{}店入驻", BalanceTypeEnum.SUBTRACT),
    USER_INVITE_USER(6, "{}直邀{}奖励", BalanceTypeEnum.SUBTRACT),
    USER_INDIRECT_INVITE_USER(7, "{}间邀{}奖励", BalanceTypeEnum.SUBTRACT),
    JIN_TONG_ACCOUNT_LOAN(8, "向金通宝贷入", BalanceTypeEnum.SUBTRACT);

    private final int subType;
    private final String describe;
    private final BalanceTypeEnum balanceType;

    @Override
    public Integer getSubType() {
        return subType;
    }

    @Override
    public String getDescribe() {
        return describe;
    }

    @Override
    public BalanceTypeEnum getBalanceType() {
        return balanceType;
    }
}
