package plus.qdt.modules.system.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 云中鹤消息类型枚举
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Getter
@AllArgsConstructor
public enum YzhMessageTypeEnum {

    /**
     * 商品消息
     */
    GOODS_MESSAGE(1, "商品消息"),

    /**
     * 销售订单消息
     */
    SALES_ORDER_MESSAGE(2, "销售订单消息"),

    /**
     * 售后订单消息
     */
    AFTER_SALE_ORDER_MESSAGE(3, "售后订单消息");

    /**
     * 消息类型码
     */
    private final Integer code;

    /**
     * 消息类型描述
     */
    private final String description;

    /**
     * 根据类型码获取枚举
     *
     * @param code 类型码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static YzhMessageTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (YzhMessageTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查类型码是否有效
     *
     * @param code 类型码
     * @return 是否有效
     */
    public static boolean isValidType(Integer code) {
        return getByCode(code) != null;
    }
}
