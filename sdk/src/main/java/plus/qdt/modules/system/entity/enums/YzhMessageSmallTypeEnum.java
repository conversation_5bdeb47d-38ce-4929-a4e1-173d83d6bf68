package plus.qdt.modules.system.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 云中鹤消息小类枚举
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Getter
@AllArgsConstructor
public enum YzhMessageSmallTypeEnum {

    // 商品消息小类 (1001-1004)
    /**
     * 商品下架
     */
    GOODS_OFF_SHELF(1001, "商品下架"),

    /**
     * 改价消息
     */
    GOODS_PRICE_CHANGE(1002, "改价消息"),

    /**
     * 停售消息
     */
    GOODS_STOP_SALE(1003, "停售消息"),

    /**
     * 新品上架消息
     */
    GOODS_ON_SHELF(1004, "新品上架消息"),

    // 销售订单消息小类 (2001-2004)
    /**
     * 销售订单待发货
     */
    SALES_ORDER_PENDING_SHIPMENT(2001, "销售订单待发货"),

    /**
     * 销售订单已发货
     */
    SALES_ORDER_SHIPPED(2002, "销售订单已发货"),

    /**
     * 销售订单已完成
     */
    SALES_ORDER_COMPLETED(2003, "销售订单已完成"),

    /**
     * 销售订单已取消
     */
    SALES_ORDER_CANCELLED(2004, "销售订单已取消"),

    // 售后订单消息小类 (3001-3005)
    /**
     * 售后订单审核通过
     */
    AFTER_SALE_ORDER_APPROVED(3001, "售后订单审核通过"),

    /**
     * 售后订单已收到货
     */
    AFTER_SALE_ORDER_RECEIVED(3002, "售后订单已收到货"),

    /**
     * 售后订单已退款
     */
    AFTER_SALE_ORDER_REFUNDED(3003, "售后订单已退款"),

    /**
     * 售后订单已生成新订单
     */
    AFTER_SALE_ORDER_NEW_ORDER_CREATED(3004, "售后订单已生成新订单"),

    /**
     * 售后审核不通过
     */
    AFTER_SALE_ORDER_REJECTED(3005, "售后审核不通过");

    /**
     * 消息小类码
     */
    private final Integer code;

    /**
     * 消息小类描述
     */
    private final String description;

    /**
     * 根据小类码获取枚举
     *
     * @param code 小类码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static YzhMessageSmallTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (YzhMessageSmallTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 检查小类码是否有效
     *
     * @param code 小类码
     * @return 是否有效
     */
    public static boolean isValidType(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 判断是否为商品相关消息
     *
     * @param code 小类码
     * @return 是否为商品相关消息
     */
    public static boolean isGoodsMessage(Integer code) {
        return code != null && code >= 1001 && code <= 1004;
    }

    /**
     * 判断是否为销售订单相关消息
     *
     * @param code 小类码
     * @return 是否为销售订单相关消息
     */
    public static boolean isSalesOrderMessage(Integer code) {
        return code != null && code >= 2001 && code <= 2004;
    }

    /**
     * 判断是否为售后订单相关消息
     *
     * @param code 小类码
     * @return 是否为售后订单相关消息
     */
    public static boolean isAfterSaleOrderMessage(Integer code) {
        return code != null && code >= 3001 && code <= 3005;
    }
}
