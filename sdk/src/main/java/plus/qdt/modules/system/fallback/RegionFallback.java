package plus.qdt.modules.system.fallback;

import plus.qdt.common.exception.ServiceException;
import plus.qdt.modules.system.client.RegionClient;
import plus.qdt.modules.system.entity.dos.Region;

/**
 * <AUTHOR>
 * @since 2.0
 */
public class RegionFallback implements RegionClient {
    @Override
    public Region getRegionById(String id) {
        throw new ServiceException();
    }

    @Override
    public String getRegionNameById(String id) {
        throw new ServiceException();
    }

    @Override
    public String getRegion(String code) {
        throw new ServiceException();
    }
}
