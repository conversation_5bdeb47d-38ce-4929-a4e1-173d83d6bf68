package plus.qdt.modules.system.fallback;

import plus.qdt.common.exception.ServiceException;
import plus.qdt.modules.system.client.SensitiveWordsClient;
import org.springframework.stereotype.Component;

/**
 * @author: ftyy
 * @date: 2022-01-17 11:30
 * @description: 敏感词Fallback
 */
public class SensitiveWordsFallback implements SensitiveWordsClient {
    @Override
    public void resetCache() {
        throw new ServiceException();
    }
}
