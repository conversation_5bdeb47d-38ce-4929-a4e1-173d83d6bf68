package plus.qdt.modules.system.entity.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 云中鹤查询消息列表请求参数
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "云中鹤查询消息列表请求参数")
public class YzhQueryMsgListRequestParam {

    @NotBlank(message = "接口授权访问令牌不能为空")
    @Schema(description = "接口授权访问令牌", required = true)
    private String accessToken;

    @NotNull(message = "消息类型不能为空")
    @Schema(description = "消息类型：1-商品消息，2-销售订单消息，3-售后订单消息", required = true)
    private Integer messageType;

    @Min(value = 1, message = "分页页码不能小于1")
    @Schema(description = "分页页码，默认为第1页")
    private Integer pageNum;

    @Min(value = 1, message = "每页数据条数不能小于1")
    @Max(value = 100, message = "每页数据条数不能超过100")
    @Schema(description = "每页数据条数，默认为100，最大不超过100条")
    private Integer pageSize;
}
