package plus.qdt.modules.system.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 定时任务状态枚举
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Getter
@AllArgsConstructor
public enum TaskStatusEnum {

    /**
     * 启用
     */
    ENABLED("ENABLED", "启用"),

    /**
     * 禁用
     */
    DISABLED("DISABLED", "禁用"),

    /**
     * 运行中
     */
    RUNNING("RUNNING", "运行中");

    /**
     * 状态码
     */
    private final String code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static TaskStatusEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        for (TaskStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 检查状态码是否有效
     *
     * @param code 状态码
     * @return 是否有效
     */
    public static boolean isValidStatus(String code) {
        return getByCode(code) != null;
    }
}
