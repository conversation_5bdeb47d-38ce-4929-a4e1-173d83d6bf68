package plus.qdt.controller.user;

import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.PageVO;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.member.entity.dos.MemberGrade;
import plus.qdt.modules.member.service.MemberGradeService;
import plus.qdt.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 会员等级接口
 *
 * <AUTHOR>
 * @since 2021/5/16 11:29 下午
 */
@RestController
@Tag(name = "会员等级接口")
@RequestMapping("/user/grade")
@RequiredArgsConstructor
public class UserGradeController {

    private final MemberGradeService memberGradeService;

    @Operation(summary = "通过id获取会员等级")
    @GetMapping(value = "/get/{id}")
    public ResultMessage<MemberGrade> get(@PathVariable String id) {

        return ResultUtil.data(memberGradeService.getById(id));
    }

    @Operation(summary = "获取会员等级分页")
    @GetMapping(value = "/getByPage")
    public ResultMessage<Page<MemberGrade>> getByPage(PageVO page) {

        return ResultUtil.data(memberGradeService.page(PageUtil.initPage(page)));
    }

    @Operation(summary = "添加会员等级")
    @PostMapping(value = "/add")
    public ResultMessage<Object> daa(@Validated  MemberGrade memberGrade) {
        if (memberGradeService.save(memberGrade)) {
            return ResultUtil.success();
        }
        throw new ServiceException();
    }

    @Operation(summary = "修改会员等级")
    @PutMapping(value = "/update/{id}")
    public ResultMessage<Object> update(@PathVariable String id,MemberGrade memberGrade) {
        if (memberGradeService.updateById(memberGrade)) {
            return ResultUtil.success();
        }
        throw new ServiceException();
    }



    @Operation(summary = "删除会员等级")
    @DeleteMapping(value = "/delete/{id}")
    public ResultMessage<Page<Object>> delete(@PathVariable String id) {
        if(Boolean.TRUE.equals(memberGradeService.getById(id).getIsDefault())){
            throw new ServiceException(ResultCode.USER_GRADE_IS_DEFAULT);
        }else if(memberGradeService.removeById(id)){
            return ResultUtil.success();
        }
        throw new ServiceException();
    }
}
