package plus.qdt.controller.feign.member;

import plus.qdt.modules.member.client.FootPrintClient;
import plus.qdt.modules.member.entity.dos.FootPrint;
import plus.qdt.modules.member.service.FootprintService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 浏览历史Feign
 *
 * @author: ftyy
 * @date: 2022-01-14 10:18
 * @description:
 */
@RestController
@RequiredArgsConstructor
public class FootprintFeignController implements FootPrintClient {

    private final FootprintService footprintService;

    @Override
    public FootPrint saveFootprint(FootPrint footPrint) {
        return footprintService.saveFootprint(footPrint);
    }

    @Override
    public boolean clean() {
        return footprintService.clean();
    }

    @Override
    public boolean deleteByIds(List<String> ids) {
        return footprintService.deleteByIds(ids);
    }

    @Override
    public long getFootprintNum() {
        return footprintService.getFootprintNum();
    }
}
