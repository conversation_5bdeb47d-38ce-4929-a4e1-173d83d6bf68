package plus.qdt.controller.feign.member;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import plus.qdt.modules.bank.Bank;
import plus.qdt.modules.member.client.BankClint;
import plus.qdt.modules.member.service.BankService;

@RestController
@RequiredArgsConstructor
public class BankFeignController implements BankClint {

    private final BankService bankService;

    @Override
    public Bank findById(String id) {
        return bankService.getById(id);
    }
}
