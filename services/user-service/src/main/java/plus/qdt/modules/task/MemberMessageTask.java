package plus.qdt.modules.task;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import plus.qdt.common.utils.DateUtil;
import plus.qdt.modules.member.entity.dos.User;
import plus.qdt.modules.member.service.UserService;
import plus.qdt.modules.message.client.MessageClient;
import plus.qdt.modules.message.entity.dos.Message;
import plus.qdt.modules.message.entity.enums.MessageRangeEnum;
import plus.qdt.modules.system.client.SmsClient;
import plus.qdt.modules.system.entity.enums.MessageType;
import plus.qdt.modules.system.entity.params.SmsParams;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户消息提醒任务调度
 *
 * <AUTHOR>
 * @since 2.0
 */
@Slf4j
@Component
public class MemberMessageTask {

    @Resource
    private UserService userService;

    @Resource
    private MessageClient messageClient;

    @Resource
    private SmsClient smsClient;

    /**
     * 会员过期消息提醒任务
     * @return {@link ReturnT} {@link String}
     * <AUTHOR>
     */
    @XxlJob("vipExpire")
    public ReturnT<String> vipExpire() {
        // 年卡到期前15天
        Date date = new Date();
        QueryWrapper<User> lqw = new QueryWrapper<>();
        lqw.eq("DATE_FORMAT(vip_expire, '%Y-%m-%d')", DateUtil.toString(DateUtil.offsetDay(date, -15), DateUtil.STANDARD_DATE_FORMAT));
        lqw.lambda().select(User::getId, User::getMobile).eq(User::getVip, true);

        Message message = new Message();
        message.setTitle(MessageType.VIP_CARD_EXPIRE.getTitle());
        message.setType(MessageType.VIP_CARD_EXPIRE.name());
        Map<String, String> content = new HashMap<>();
        String messageResult = "您的会员年卡仅剩15天即将过期，为了不影响正常分红请您尽快续期！";
        content.put("message", messageResult);
        content.put("path", MessageType.VIP_CARD_EXPIRE.getPath()); // 跳转页面
        message.setContent(JSONUtil.toJsonStr(content));
        message.setMessageRange(MessageRangeEnum.USER.name());
        List<User> list = userService.list(lqw);
        if (!list.isEmpty()) {
            message.setUserIds(list.stream().map(User::getId).toArray(String[]::new));
            // 发送站内信
            messageClient.sendMessage(message);
            // TODO 发送短信提醒
            SmsParams smsParams = SmsParams.builder().signName("")
                    .mobiles(list.stream().map(User::getMobile).toList())
                    .templateCode("").build();
//        smsClient.sendSmsCode(smsParams);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 平台生日祝福任务调度
     * @return {@link ReturnT} {@link String}
     * <AUTHOR>
     */
    @XxlJob("userBirthdays")
    public ReturnT<String> userBirthdays() {
        QueryWrapper<User> lqw = new QueryWrapper<>();
        lqw.eq("DATE_FORMAT(birthday,'%m-%d')", DateUtil.toString(new Date(), "MM-dd")); // 今天
        lqw.lambda().select(User::getId, User::getMobile); // 实名过的才提醒
        Message message = new Message();
        message.setTitle(MessageType.QDT_BLESSING.getTitle());
        message.setType(MessageType.QDT_BLESSING.name());
        String messageResult = "企道通+24祝您生日快乐，步步高升财源滚滚！";
        message.setContent(messageResult);
        message.setMessageRange(MessageRangeEnum.USER.name());
        List<User> list = userService.list(lqw);
        if (!list.isEmpty()) {
            message.setUserIds(list.stream().map(User::getId).toArray(String[]::new));
            // 发送站内信
            messageClient.sendMessage(message);
        }
        return ReturnT.SUCCESS;
    }

}
