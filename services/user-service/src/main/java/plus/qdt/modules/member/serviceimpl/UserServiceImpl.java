package plus.qdt.modules.member.serviceimpl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.EnumUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.AllArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import plus.qdt.cache.Cache;
import plus.qdt.cache.CachePrefix;
import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.event.TransactionCommitSendMQEvent;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.security.AuthUser;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.common.utils.*;
import plus.qdt.common.vo.PageVO;
import plus.qdt.exchange.AmqpExchangeProperties;
import plus.qdt.modules.domain.param.RealAuthenticationParam;
import plus.qdt.modules.domain.vo.RealAuthenticationVo;
import plus.qdt.modules.im.client.ImTencentClient;
import plus.qdt.modules.im.entity.dto.UserSetting;
import plus.qdt.modules.jinTongAccount.client.AccountConsumeClient;
import plus.qdt.modules.jinTongAccount.client.AmountClient;
import plus.qdt.modules.jinTongAccount.entity.AccountConsume;
import plus.qdt.modules.jinTongAccount.enums.AccountTypeEnum;
import plus.qdt.modules.jinTongAccount.enums.LoanPoolLogEnum;
import plus.qdt.modules.jinTongAccount.enums.UserQiAmountLogEnum;
import plus.qdt.modules.member.client.ConnectClient;
import plus.qdt.modules.member.entity.dos.User;
import plus.qdt.modules.member.entity.dto.*;
import plus.qdt.modules.member.entity.enums.PromotionGrade;
import plus.qdt.modules.member.entity.vo.UserVO;
import plus.qdt.modules.member.mapper.UserMapper;
import plus.qdt.modules.member.service.ShareLogService;
import plus.qdt.modules.member.service.UserLabelService;
import plus.qdt.modules.member.service.UserService;
import plus.qdt.modules.permission.entity.dto.UserAdminDTO;
import plus.qdt.modules.sensitive.SensitiveWordsFilter;
import plus.qdt.modules.share.ShareLog;
import plus.qdt.modules.system.client.RegionClient;
import plus.qdt.modules.system.client.SettingClient;
import plus.qdt.mybatis.util.PageUtil;
import plus.qdt.routing.UserRoutingKey;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

/**
 * 用户接口业务层实现
 *
 * <AUTHOR>
 * @since 2021-03-29 14:10:16
 */
@Service
@AllArgsConstructor
@GlobalTransactional
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private final ApplicationEventPublisher applicationEventPublisher;
    private final AmqpExchangeProperties amqpExchangeProperties;
    private final UserLabelService userLabelService;
    private final ConnectClient connectClient;
    private final RegionClient regionClient;
    private final ImTencentClient imTencentClient;
    private final Cache<Object> cache;
    private SettingClient settingClient;
    private final ShareLogService shareService;
    private final AmountClient amountClient;
    private final AccountConsumeClient accountConsumeClient;

    @Override
    public User findByUsername(String userName, SceneEnums scene) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getUsername, userName);
        queryWrapper.eq(User::getScene, scene.value());
        return this.baseMapper.selectOne(queryWrapper);
    }


    @Override
    public UserVO userInfo(String userId) {
        User user = this.getById(userId);
        UserVO userVO = new UserVO(user);
        if (StringUtils.isNotBlank(user.getAreaCode())) {
            userVO.setRegion(regionClient.getRegion(user.getAreaCode()));
        }
        return userVO;
    }

    @Override
    public User getById(Serializable id, boolean exception) {
        User user = super.getById(id);
        if (user == null) {
            // 删除缓存
            List<String> keys = cache.keys(CachePrefix.ACCESS_TOKEN.getPrefix(SceneEnums.MEMBER, id.toString()) + "*");
            cache.multiDel(keys);
            if (exception) {
                throw new ServiceException(ResultCode.USER_NOT_EXIST);
            } else {
                return null;
            }
        }
        LambdaUpdateWrapper<ShareLog> luw = new LambdaUpdateWrapper<>();
        luw.eq(ShareLog::getSharedId, user.getId());
        // 获取用户推广等级信息
        // 2. 36个以上直推购买了村代(镇代、县代)礼包的 表示城市合伙人
        ShareLog share = shareService.getByUserId(user.getId());
        if (share != null) {
            user.setPromotionGrade(EnumUtil.getBy(PromotionGrade::getGrade, share.getIdentity() == null ? 1 : share.getIdentity()));
        } else {
            user.setPromotionGrade(PromotionGrade.ORDINARY);
        }
        return user;
    }

    @Override
    public User userLoginQuery(UserLoginDTO userLoginDTO) {
        User user = this.findByUsername(userLoginDTO.getUsername(), userLoginDTO.getScene());
        //判断用户是否存在
        if (user == null) {
            throw new ServiceException(ResultCode.USER_NOT_EXIST);
        }
        //判断用户是否有效
        if (user.getEnable().equals(false) || user.getDeleteFlag().equals(true)) {
            throw new ServiceException(ResultCode.USER_DISABLE);
        }
        //判断密码是否输入正确
        if (!new BCryptPasswordEncoder().matches(userLoginDTO.getPassword(), user.getPassword())) {
            throw new ServiceException(ResultCode.USER_PASSWORD_ERROR);
        }
        // 获取推广者
        this.insertStarLog(userLoginDTO.getPromotionCode(), user.getId(), user.getNickName(), user.getFace(), false);
        return user;
    }

    @Override
    @Transactional
    public User mobilePhoneLogin(String mobilePhone, SceneEnums scene, String promotionCode) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("mobile", mobilePhone);
        queryWrapper.eq("scene", scene.value());
        User user = this.baseMapper.selectOne(queryWrapper);
        //如果手机号不存在则自动注册用户
        if (user == null) {
            UserInfoDTO userInfoDTO =
                    UserInfoDTO.builder().username(mobilePhone).mobile(mobilePhone).scene(scene).password(UserService.DEFAULT_PASSWORD).build();

            user = registerHandler(userInfoDTO);
            // 获取推广者
            this.insertStarLog(promotionCode, user.getId(), user.getNickName(), user.getFace(), false);
        }
        //判断用户是否有效
        if (user.getEnable().equals(false) || user.getDeleteFlag().equals(true)) {
            throw new ServiceException(ResultCode.USER_DISABLE);
        }
        return user;
    }


    @Override
    public void editOwn(UserEditBaseDTO userEditBaseDTO) {
        //查询用户信息
        String userId = StringUtils.isNotBlank(userEditBaseDTO.getId()) ? userEditBaseDTO.getId() : UserContext.getCurrentExistUser().getId();
        LambdaUpdateWrapper<User> luw = new LambdaUpdateWrapper<User>().eq(User::getId, userId)
                .set(StringUtils.isNotBlank(userEditBaseDTO.getNickName()), User::getNickName, userEditBaseDTO.getNickName())
                .set(StringUtils.isNotBlank(userEditBaseDTO.getFace()), User::getFace, userEditBaseDTO.getFace())
                .set(StringUtils.isNotBlank(userEditBaseDTO.getAreaCode()), User::getAreaCode, userEditBaseDTO.getAreaCode())
                .set(userEditBaseDTO.getSex() != null, User::getSex, userEditBaseDTO.getSex())
                .set(userEditBaseDTO.getBirthday() != null, User::getBirthday, userEditBaseDTO.getBirthday());
        //修改用户
        if (this.update(luw) && UserContext.getCurrentExistUser().getScene().equals(SceneEnums.MEMBER)) {
            // 同步设置im信息
            UserSetting setting = new UserSetting().setUserId(userId)
                    .nickname(userEditBaseDTO.getNickName())
                    .avatar(userEditBaseDTO.getFace())
                    .sex(userEditBaseDTO.getSex())
                    .birthday(userEditBaseDTO.getBirthday());
            if (StringUtils.isNotBlank(userEditBaseDTO.getAreaCode())) {
                setting.address(regionClient.getRegion(userEditBaseDTO.getAreaCode()));
            }
            if (!setting.getItems().isEmpty()) {
                imTencentClient.setImProfile(setting);
            }
        }
    }


    @Override
    @Transactional
    public User addUser(UserInfoDTO userInfoDTO) {
        //检测用户信息
        checkMember(userInfoDTO.getUsername(), userInfoDTO.getMobile(), userInfoDTO.getScene().value());
        //添加用户
        userInfoDTO.setPassword(new BCryptPasswordEncoder().encode(userInfoDTO.getPassword()));
        return registerHandler(userInfoDTO);
    }

    @Override
    @Transactional
    public UserVO updateUser(UserInfoDTO userInfoDTO) {
        //过滤用户昵称敏感词
        if (CharSequenceUtil.isNotBlank(userInfoDTO.getNickName())) {
            userInfoDTO.setNickName(SensitiveWordsFilter.filter(userInfoDTO.getNickName()));
        }
        //如果密码不为空则加密密码
        if (CharSequenceUtil.isNotBlank(userInfoDTO.getPassword())) {
            userInfoDTO.setPassword(new BCryptPasswordEncoder().encode(userInfoDTO.getPassword()));
        }

        if (userInfoDTO.getBirthday() != null && userInfoDTO.getBirthday().after(new Date())) {
            throw new ServiceException(ResultCode.PARAMS_ERROR, "生日不能大于当前日期");
        }
        //查询用户信息
        User user = this.getById(userInfoDTO.getId());
        //编辑用户时需要先判定用户信息是否可以编辑，判定依据为：修改用户的逻辑场景是否与数据库中一致，避免跨业务修改
        if (user == null || !SceneEnums.getScene(user.getScene()).equals(userInfoDTO.getScene())) {
            throw new ServiceException(ResultCode.USER_NOT_EXIST);
        }
        //2024-04-12 fixBug ：用户名单独处理，修改用户不应该修改用户 username 字段
        String userName = user.getUsername();
        //传递修改用户信息
        BeanUtil.copyProperties(userInfoDTO, user);
        user.setUsername(userName);
        this.updateById(user);
        cache.vagueDel(CachePrefix.USER_MENU.getPrefix(SceneEnums.valueOf(user.getScene()), user.getId()));
        cache.vagueDel(CachePrefix.PERMISSION_LIST.getPrefix(SceneEnums.valueOf(user.getScene()), user.getId()));

        applicationEventPublisher.publishEvent(TransactionCommitSendMQEvent.builder().source("USER_UPDATE").exchange(amqpExchangeProperties.getUser()).routingKey(UserRoutingKey.USER_UPDATE).message(user).build());
        return new UserVO(user);
    }

    @Override
    public Page<UserVO> getUserPage(UserSearchParams userSearchParams, PageVO page) {
        QueryWrapper<User> queryWrapper = Wrappers.query();
        //用户名查询
        queryWrapper.eq(CharSequenceUtil.isNotBlank(userSearchParams.getUsername()), "username", userSearchParams.getUsername());
        //用户名查询
        queryWrapper.eq(CharSequenceUtil.isNotBlank(userSearchParams.getNickName()), "nick_name", userSearchParams.getNickName());
        //按照电话号码查询
        queryWrapper.eq(CharSequenceUtil.isNotBlank(userSearchParams.getMobile()), "mobile", userSearchParams.getMobile());
        //按照标签名称查询
        queryWrapper.eq(CharSequenceUtil.isNotBlank(userSearchParams.getLabelId()), "label_ids", userSearchParams.getLabelId());
        //按照用户状态查询
        queryWrapper.eq(userSearchParams.getEnable() != null, "enable", userSearchParams.getEnable()); //按照用户状态查询
        //必填场景参数
        queryWrapper.eq("scene", userSearchParams.getScene().name());

        queryWrapper.orderByDesc("create_time");
        Page<UserVO> pageUserVO = this.baseMapper.memberPage(PageUtil.initPage(page), queryWrapper);
        for (UserVO record : pageUserVO.getRecords()) {
            record.setLabelNames(userLabelService.getLabelNames(record.getLabelIds()));
        }

        pageUserVO.setRecords(pageUserVO.getRecords().stream().peek(userVO -> userVO
                .setLabelNames(userLabelService.getLabelNames(userVO.getLabelIds()))).toList());

        return pageUserVO;
    }


    /**
     * 获取用户列表
     *
     * @param searchParams 查询参数
     * @return 用户列表
     */
    @Override
    public List<User> list(UserSearchParams searchParams) {
        if (searchParams.getPageNumber() > 0 && searchParams.getPageSize() > 0) {
            return this.page(PageUtil.initPage(searchParams), searchParams.queryWrapper()).getRecords();
        }
        return this.list(searchParams.queryWrapper());
    }


    @Override
    public Boolean updateUserStatus(UserStatusChangeDTO userStatusChangeDTO) {
        LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(User::getScene, userStatusChangeDTO.getScene().value());
        updateWrapper.set(User::getEnable, userStatusChangeDTO.getEnable());
        updateWrapper.in(User::getId, userStatusChangeDTO.getUserIds());
        boolean update = this.update(updateWrapper);
        if (update && !userStatusChangeDTO.getEnable()) {
            for (String userId : userStatusChangeDTO.getUserIds()) {
                cache.vagueDel(CachePrefix.ACCESS_TOKEN.getPrefix(userStatusChangeDTO.getScene(), userId));
                cache.vagueDel(CachePrefix.REFRESH_TOKEN.getPrefix(userStatusChangeDTO.getScene(), userId));
            }
        }
        return update;
    }

    @Override
    public long getMemberNum(UserSearchParams userSearchParams) {
        QueryWrapper<User> queryWrapper = Wrappers.query();
        //用户名查询
        queryWrapper.eq(CharSequenceUtil.isNotBlank(userSearchParams.getUsername()), "username", userSearchParams.getUsername());
        //按照电话号码查询
        queryWrapper.eq(CharSequenceUtil.isNotBlank(userSearchParams.getMobile()), "mobile", userSearchParams.getMobile());
        //按照状态查询
        queryWrapper.eq(userSearchParams.getEnable() != null, "enable", userSearchParams.getEnable());
        //场景筛选
        queryWrapper.eq("scene", userSearchParams.getScene().name());
        if (userSearchParams.getMobileFlag() != null && userSearchParams.getMobileFlag()) {
            queryWrapper.isNotNull("mobile");
        }
        queryWrapper.orderByDesc("create_time");
        return this.count(queryWrapper);
    }

    /**
     * 获取指定用户数据
     *
     * @param columns          指定获取的列
     * @param userSearchParams 用户搜索VO
     * @return 指定用户数据
     */
    @Override
    public List<Map<String, Object>> listFieldsByCondition(String columns, UserSearchParams userSearchParams) {
        QueryWrapper<User> queryWrapper = userSearchParams.queryWrapper();
        return this.listMaps(queryWrapper.select(columns));
    }

    /**
     * 获取所有用户的手机号
     *
     * @return 所有用户的手机号
     */
    @Override
    public List<String> getMemberMobile() {
        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(User::getEnable, true);
        lambdaQueryWrapper.eq(User::getScene, SceneEnums.MEMBER.name());
        lambdaQueryWrapper.isNotNull(User::getMobile);
        return this.baseMapper.getAllMemberMobile(lambdaQueryWrapper);
    }

    /**
     * 更新用户登录时间为最新时间
     *
     * @param userId 用户id
     * @return 是否更新成功
     */
    @Override
    public Boolean updateUserLoginTime(String userId) {
        LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(User::getId, userId);
        updateWrapper.set(User::getLastLoginDate, new Date());
        return this.update(updateWrapper);
    }


    /**
     * 注册方法抽象
     *
     * @param userInfoDTO 注册传输对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public User registerHandler(UserInfoDTO userInfoDTO) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.and(qw -> qw.eq(User::getUsername, userInfoDTO.getUsername()).or().eq(User::getMobile, userInfoDTO.getMobile()));
        queryWrapper.eq(User::getScene, userInfoDTO.getScene().value());
        if (this.count(queryWrapper) > 0) {
            throw new ServiceException(ResultCode.USER_EXIST);
        }
        User user = new User(userInfoDTO);
        user.setId(SnowFlake.getIdStr());
        //保存用户
        this.save(user);
        UserContext.settingInviter(user.getId(), cache);
        // 同步IM
        imTencentClient.syncIm(user);
        // 开通钱包
        amountClient.openAmount(user.getId());
        applicationEventPublisher.publishEvent(TransactionCommitSendMQEvent.builder().source("USER_REGISTER").exchange(amqpExchangeProperties.getUser()).routingKey(UserRoutingKey.USER_REGISTER).message(user).build());
        return user;
    }


    @Override
    public void saveUser(UserAdminDTO userAdminDTO) {
        this.save(new User(userAdminDTO));
    }

    @Override
    public void updateSceneId(String userId, String sceneId) {
        LambdaUpdateWrapper<User> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(User::getExtendId, sceneId);
        updateWrapper.eq(User::getId, userId);
        this.update(updateWrapper);
    }


    @Override
    public List<User> getUserByDepartmentIds(List<String> departmentIds) {
        if (departmentIds == null || departmentIds.isEmpty()) {
            return new ArrayList<>();
        }
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("department_id", departmentIds);
        return this.list(queryWrapper);
    }

    @Override
    public boolean updateUserLabel(UserInfoDTO userInfoDTO) {
        if (userInfoDTO == null || !CharSequenceUtil.isNotBlank(userInfoDTO.getId())) {
            throw new ServiceException(ResultCode.USER_NOT_EXIST);
        }
        if (userInfoDTO.getLabelIds() == null) {
            throw new ServiceException(ResultCode.USER_LABEL_NOT_EXIST);
        }
        LambdaUpdateWrapper<User> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(User::getLabelIds, userInfoDTO.getLabelIdsStr());
        updateWrapper.eq(User::getId, userInfoDTO.getId());
        return this.update(updateWrapper);
    }

    @Override
    public boolean isBindLabel(String labelId) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("label_ids", labelId);
        return this.count(queryWrapper) > 0;
    }

    @Override
    public void logoff() {
        AuthUser authUser = UserContext.getCurrentExistUser();
        //标记删除
        User user = this.getById(authUser.getId());
        user.setDeleteFlag(true);
        this.updateById(user);
    }

    @Override
    public void logoffDelete() {

        //删除半年前注销的用户
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("delete_flag", true);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MONTH, -6);
        queryWrapper.lt("update_time", calendar.getTime());
        List<User> users = this.list(queryWrapper);
        this.remove(queryWrapper);

        //删除用户后其他业务
        users.forEach(user -> {
            try {
                connectClient.deleteConnect(user.getId());
            } catch (Exception e) {
                log.error("删除用户联合登陆信息失败", e);
            }

        });
    }

    @Override
    public void cancellation() {
        AuthUser authUser = UserContext.getCurrentExistUser();
        User user = this.getById(authUser.getId());
        //删除联合登录
        connectClient.deleteConnect(user.getId());
        //混淆用户信息
        this.confusionUser(user);
    }

    @Override
    public void insertStarLog(String promotionCode, String userId, String name, String image, boolean autoException) {
        // 获取推广者
        if (StringUtils.isNotBlank(promotionCode)) {
            User codeUser = this.baseMapper.selectOne(Wrappers.<User>lambdaQuery()
                    .eq(User::getPromotionCode, promotionCode).select(User::getId));
            if (codeUser != null) {
                //是否有推荐码--判断推广码是否有效
                ShareLog share = shareService.getByUserId(userId);
                //未被分享过才添加
                if (share == null) {
                    ShareLog shareLog = new ShareLog();
                    shareLog.setId(SnowFlake.getId());
                    shareLog.setShareStatus(autoException ? 1 : 0);
                    shareLog.setShareId(codeUser.getId());
                    shareLog.setSharedId(userId);
                    shareLog.setRecommendCode(promotionCode);
                    shareLog.setImage(image);
                    shareLog.setName(name);
                    shareLog.setCreateTime(new Date());
                    shareService.save(shareLog);
                    // 实名才会奖励
                    if (autoException) {
                        // 邀请后奖励直邀人和间邀人
                        this.invitationRewards(shareLog);
                        // 是否满足父级推广者是否满足晋升创业者身份
                        shareService.isPromotion(userId, PromotionGrade.ORDINARY);
                    }
                }
            } else {
                if (autoException) {
                    throw new ServiceException("推广码不存在");
                }
            }
        }
    }

    /**
     * 邀请奖励：直邀+间邀
     * @param share 邀请信息
     * <AUTHOR>
     */
    private void invitationRewards(ShareLog share) {
        User user = this.getById(share.getShareId(), false);
        if (user == null) {
            return;
        }
        User invitationUser = this.getById(share.getSharedId(), false);
        String username = DesensitizedUtil.mobilePhone(invitationUser.getMobile());
        // 借贷池出账-用户企通宝入账
        this.invitations(LoanPoolLogEnum.USER_INVITE_USER, UserQiAmountLogEnum.DIRECT_INVITE,
                settingClient.getQdtPay().getDirectPush(),
                share.getShareId(), user.getNickName(), username);

        // 获取间邀用户
        ShareLog invitations = shareService.getByUserId(share.getShareId());
        if (invitations == null) {
            return;
        }
        User invitationsUser = this.getById(invitations.getShareId(), false);
        if (invitationsUser != null) {
            // 借贷池出账-用户企通宝入账
            this.invitations(LoanPoolLogEnum.USER_INDIRECT_INVITE_USER, UserQiAmountLogEnum.INDIRECT_INVITE,
                    settingClient.getQdtPay().getInterPush(),
                    invitations.getShareId(), invitationsUser.getNickName(), username);
        }
    }

    /**
     * 金额操作API
     *
     * @param subType 操作子类型
     * @param money   金额
     * @param userId  操作用户
     * @param nickname  邀请人
     * @param username  被邀请人
     * <AUTHOR>
     */
    private void invitations(LoanPoolLogEnum subType, UserQiAmountLogEnum userType, Integer money, String userId, String nickname, String username) {
        // 用户企通入账
        AccountConsume invitations = new AccountConsume();
        // 借贷池出账
        invitations.setAccountTypeEnum(AccountTypeEnum.LOAN_POOL);
        invitations.setSubType(subType.getSubType());
        // xx邀请xx
        invitations.setNames(List.of(nickname, username));
        // 奖励金额
        invitations.setMoney(BigDecimal.valueOf(money));
        invitations.setId(userId);
        accountConsumeClient.jinTongAccountConsume(invitations);
        // 用户企通入账
        this.accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                .setId(userId)
                .setAccountTypeEnum(AccountTypeEnum.USER_QI_TONG_ACCOUNT)
                .setSubType(userType.getSubType())
                .setNames(List.of(username))
                .setMoney(BigDecimal.valueOf(money))
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RealAuthenticationVo submitRealAuthentication(RealAuthenticationParam params) {
        AuthUser authUser = UserContext.getCurrentExistUser();
        User user = this.baseMapper.selectById(authUser.getId());
        // 查询自己是否是被推广的
        ShareLog shareLog = shareService.getByUserId(user.getId());
        if (shareLog == null) {
            // 新增推广信息
            this.insertStarLog(params.getPromotionCode(), user.getId(), user.getRealName(), user.getFace(), true);
        } else {
            // 邀请后奖励直邀人和间邀人
            this.invitationRewards(shareLog);
            // 是否满足父级推广者是否满足晋升创业者身份
            shareService.isPromotion(user.getId(), PromotionGrade.ORDINARY);
        }
        user.setRealName(params.getName());
        user.setIdCard(params.getIdNumber());
        user.setIdCardFront(params.getIdCardFront());
        user.setIdCardBack(params.getIdCardBack());
        user.setRealAuthentication(true);
        user.setAddress(params.getAddress());
        user.setSex(params.getSex());
        user.setAreaCode(params.getAreaCode());
        user.setBirthday(DateUtil.toDate(params.getBirthday(), DateUtil.STANDARD_DATE_NO_UNDERLINE_FORMAT));
        user.setTimeLimit(params.getTimeLimit());
        if (params.getTimeLimit().contains(StringUtils.DASHED)) {
            if (params.getTimeLimit().equals("长期")) {
                user.setTimeLimit("长期");
            } else {
                String[] split = params.getTimeLimit().split(StringUtils.DASHED);
                String startTime = DateUtil.toString(DateUtil.toDate(split[0], DateUtil.STANDARD_DATE_NO_UNDERLINE_FORMAT), DateUtil.DOT_DATE_FORMAT);
                String endTime = DateUtil.toString(DateUtil.toDate(split[1], DateUtil.STANDARD_DATE_NO_UNDERLINE_FORMAT), DateUtil.DOT_DATE_FORMAT);
                user.setTimeLimit(startTime + "-" + endTime);
            }
        }
        user.setBirthday(DateUtil.toDate(params.getTimeLimit(), DateUtil.STANDARD_DATE_NO_UNDERLINE_FORMAT));
        // 生产推广码
        user.setPromotionCode(CommonUtil.generateRandomCode(8));
        this.baseMapper.updateById(user);
        return new RealAuthenticationVo().setRealAuthentication(true)
                .setPromotionCode(user.getPromotionCode())
                .setIdcard(user.getIdCard())
                .setName(user.getRealName());
    }

    @Override
    public Boolean checkPromotionCode(String promotionCode) {
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("promotion_code", promotionCode);
        return this.baseMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public String getPromotionCode() {
        QueryWrapper<ShareLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("shared_id", UserContext.getCurrentExistUser().getId());
        queryWrapper.select("recommend_code");
        ShareLog shareLog = this.shareService.getOne(queryWrapper);
        if (shareLog != null) {
            return shareLog.getRecommendCode();
        }
        return null;
    }

    /**
     * 混淆之前的会员信息
     *
     * @param user 会员
     */
    private void confusionUser(User user) {
        user.setUsername(CommonUtil.getUUID());
        user.setMobile(CommonUtil.getUUID() + user.getMobile());
        user.setNickName("用户已注销");
        user.setEnable(false);
        user.setDeleteFlag(true);
        this.updateById(user);
        // 删除缓存
        List<String> keys = cache.keys(CachePrefix.ACCESS_TOKEN.getPrefix(SceneEnums.MEMBER, user.getId()) + "*");
        cache.multiDel(keys);
    }

    /**
     * 检测用户
     *
     * @param userName    用户名称
     * @param mobilePhone 手机号
     */
    private void checkMember(String userName, String mobilePhone, String scene) {
        //判断手机号是否存在
        if (findUser(mobilePhone, userName, scene) > 0) {
            throw new ServiceException(ResultCode.USER_EXIST);
        }
    }


    /**
     * 根据手机号获取用户
     *
     * @param mobilePhone 手机号
     * @param userName    用户名
     * @param scene       场景
     * @return 用户
     */
    private Long findUser(String mobilePhone, String userName, String scene) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getScene, scene).and(i -> i.eq(User::getMobile, mobilePhone).or().eq(User::getUsername, userName));
        return this.baseMapper.selectCount(queryWrapper);
    }

}