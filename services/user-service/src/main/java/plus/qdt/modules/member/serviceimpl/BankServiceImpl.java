package plus.qdt.modules.member.serviceimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.utils.SnowFlake;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.bank.Bank;
import plus.qdt.modules.third.ShumaiApi;
import plus.qdt.modules.member.entity.dos.User;
import plus.qdt.modules.member.mapper.BankMapper;
import plus.qdt.modules.member.service.BankService;
import plus.qdt.modules.member.service.UserService;

import java.io.Serializable;
import java.util.List;

@Service
@RequiredArgsConstructor
public class BankServiceImpl extends ServiceImpl<BankMapper, Bank> implements BankService {

    private final UserService userService;

    @Override
    public Bank getById(Serializable id) {
        Bank bank = super.getById(id);
        User user = userService.getById(bank.getUserId());
        bank.setUsername(user.getRealName());
        return bank;
    }

    @Override
    public ResultMessage<Bank> add(Bank bank) {
        // 校验用户是否实名验证
        User user = userService.getById(UserContext.getCurrentId());
        if (user.getRealAuthentication() == null || !user.getRealAuthentication()) {
            return ResultUtil.error("您未进行实名认证，请实名后操作！");
        }
        QueryWrapper<Bank> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bank_code", bank.getBankCode());
        queryWrapper.eq("delete_flag", 0);
        Bank bankOne = this.baseMapper.selectOne(queryWrapper);
        ResultMessage<Bank> bankResultMessage = new ResultMessage<>();
        //查询用户绑定银行卡数量
        QueryWrapper<Bank> query = new QueryWrapper<>();
        query.eq("user_id", bank.getUserId());
        query.eq("delete_flag", 0);
        Long count = baseMapper.selectCount(query);
        if (count >= 10) {
            bankResultMessage.setMessage("该用户绑定银行卡已超过10张，请解绑其它银行卡后再添加！");
            bankResultMessage.setSuccess(false);
            return bankResultMessage;
        }
        if (bankOne == null) {
            // 第三方银行卡要素验证
            ShumaiApi.bankCardVerify(bank.getBankCode(), user.getIdCard(), user.getRealName(), bank.getMobile());
            bank.setId(SnowFlake.getIdStr());
            this.baseMapper.insert(bank);
            bankResultMessage.setSuccess(true);
            bankResultMessage.setMessage("银行卡添加成功！");
        } else {
            bankResultMessage.setSuccess(false);
            bankResultMessage.setMessage("该银行卡已存在！");
        }
        bankResultMessage.setResult(bank);
        return bankResultMessage;
    }

    @Override
    public List<Bank> findUserBankByUserId(String id) {
        LambdaQueryWrapper<Bank> lqw = new LambdaQueryWrapper<>();
        lqw.eq(Bank::getUserId, id);
        return this.baseMapper.selectList(lqw);
    }

    @Override
    public ResultMessage<Bank> unbindBank(String id) {
        ResultMessage<Bank> bankResultMessage = new ResultMessage<>();
        Bank bank = new Bank();
        bank.setDeleteFlag(true);
        bank.setId(id);
        this.baseMapper.updateById(bank);
        bankResultMessage.setMessage("该银行卡解绑成功!");
        return bankResultMessage;
    }
}
