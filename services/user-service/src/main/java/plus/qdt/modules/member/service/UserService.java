package plus.qdt.modules.member.service;

import cn.hutool.crypto.digest.MD5;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.common.vo.PageVO;
import plus.qdt.modules.domain.param.RealAuthenticationParam;
import plus.qdt.modules.domain.vo.RealAuthenticationVo;
import plus.qdt.modules.member.entity.dos.User;
import plus.qdt.modules.member.entity.dto.*;
import plus.qdt.modules.member.entity.vo.UserVO;
import plus.qdt.modules.permission.entity.dto.UserAdminDTO;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 用户业务层
 *
 * <AUTHOR>
 * @since 2020-02-25 14:10:16
 */
public interface UserService extends IService<User> {

    String DEFAULT_PASSWORD = new BCryptPasswordEncoder().encode(MD5.create().digestHex("123456a"));

    /**
     * 获取当前登录的用户信息
     *
     * @return 用户信息
     */
    default UserVO userInfo() {
        return this.userInfo(UserContext.getCurrentExistUser().getId());
    }

    /**
     * 获取当前登录的用户信息
     *
     * @return 用户信息
     */
    UserVO userInfo(String userId);

    User getById(Serializable id, boolean exception);

    @Override
    default User getById(Serializable id) {
        return this.getById(id, true);
    }

    /**
     * 通过用户名获取用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    User findByUsername(String username, SceneEnums scene);

    /**
     * 用户登录
     *
     * @param userLoginDTO 用户登录查询
     * @return token
     */
    User userLoginQuery(UserLoginDTO userLoginDTO);

    /**
     * 手机号注册登录
     *
     * @param mobilePhone 手机号
     * @param scene       场景
     * @return user
     */
    User mobilePhoneLogin(String mobilePhone, SceneEnums scene, String promotionCode);

    /**
     * 修改用户信息
     *
     * @param userEditBaseDTO 用户修改信息
     */
    void editOwn(UserEditBaseDTO userEditBaseDTO);

    /**
     * 后台-添加用户
     *
     * @param userInfoDTO 用户
     * @return 用户
     */
    User addUser(UserInfoDTO userInfoDTO);

    /**
     * 后台-修改用户
     *
     * @param userInfoDTO 后台修改用户参数
     * @return 用户
     */
    UserVO updateUser(UserInfoDTO userInfoDTO);

    /**
     * 获取用户分页
     *
     * @param userSearchParams 用户搜索VO
     * @param page             分页
     * @return 用户分页
     */
    Page<UserVO> getUserPage(UserSearchParams userSearchParams, PageVO page);

    /**
     * 获取用户列表
     *
     * @param searchParams 查询参数
     * @return 用户列表
     */
    List<User> list(UserSearchParams searchParams);


    /**
     * 修改用户状态
     *
     * @param userStatusChangeDTO 用户状态修改DTO
     * @return 修改结果
     */
    Boolean updateUserStatus(UserStatusChangeDTO userStatusChangeDTO);

    /**
     * 根据条件查询用户总数
     *
     * @param userSearchParams 用户搜索VO
     * @return 用户总数
     */
    long getMemberNum(UserSearchParams userSearchParams);

    /**
     * 获取指定用户数据
     *
     * @param columns 指定获取的列
     * @param userSearchParams 用户ids
     * @return 指定用户数据
     */
    List<Map<String, Object>> listFieldsByCondition(String columns, UserSearchParams userSearchParams);


    /**
     * 获取所有用户的手机号
     *
     * @return 所有用户的手机号
     */
    List<String> getMemberMobile();

    /**
     * 更新用户登录时间为最新时间
     *
     * @param userId 用户id
     * @return 是否更新成功
     */
    Boolean updateUserLoginTime(String userId);

    /**
     * 新增用户
     *
     * @param adminUser
     */
    void saveUser(UserAdminDTO adminUser);

    /**
     * 注册用户
     *
     * @param userInfoDTO
     * @return
     */
    User registerHandler(UserInfoDTO userInfoDTO);


    /**
     * 修改场景ID
     *
     * @param userId  用户id
     * @param sceneId 场景下id
     */
    void updateSceneId(String userId, String sceneId);

    /**
     * 是否有用户引用了该部门
     *
     * @param departmentIds 部门id
     * @return 是否有用户引用了该部门
     */
    List<User> getUserByDepartmentIds(List<String> departmentIds);

    /**
     * 修改会员标签
     *
     * @param userInfoDTO 用户信息
     * @return 是否有用户引用了该部门
     */
    boolean updateUserLabel(UserInfoDTO userInfoDTO);

    /**
     * 标签是否绑定用户
     *
     * @param labelId 标签id
     * @return 是否绑定
     */
    boolean isBindLabel(String labelId);

    /**
     * 注销账号
     */
    void logoff();
    /**
     * 注销账号
     */
    void logoffDelete();

    /**
     * 注销账号
     *
     */
    void cancellation();

    /**
     * 分享日志
     */
    void insertStarLog(String promotionCode,String userId,String name,String image, boolean autoException);

    /**
     * 提交实名认证结果
     * @param params 提交参数
     * @return {@link User}
     * <AUTHOR>
     */
    RealAuthenticationVo submitRealAuthentication(RealAuthenticationParam params);

    /**
     * 判断邀请码是否有效
     * @param promotionCode
     * @return
     */
    Boolean checkPromotionCode(String promotionCode);

    /**
     * 获取邀请我的码
     * @return {@link String}
     * <AUTHOR>
     */
    String getPromotionCode();
}