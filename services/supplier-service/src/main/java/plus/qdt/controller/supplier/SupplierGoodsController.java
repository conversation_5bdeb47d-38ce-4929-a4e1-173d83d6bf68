package plus.qdt.controller.supplier;

import plus.qdt.cache.Cache;
import plus.qdt.cache.CachePrefix;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.utils.SnowFlake;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.goods.client.GoodsClient;
import plus.qdt.modules.goods.client.YzhGoodsClient;
import plus.qdt.modules.goods.entity.dto.GoodsInitProgressDTO;
import plus.qdt.modules.goods.entity.dto.GoodsOperationDTO;
import plus.qdt.modules.supplier.entity.dto.SupplierGoodsOperationDTO;
import plus.qdt.modules.supplier.service.SupplierGoodsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 供应商,商品接口
 *
 * <AUTHOR>
 * @since 2020-02-23 15:18:56
 */
@RestController
@Tag(name = "供应商端,商品接口")
@RequestMapping("/supplier/goods")
@RequiredArgsConstructor
@Slf4j
public class SupplierGoodsController {

    private final SupplierGoodsService supplierGoodsService;

    private final GoodsClient goodsClient;

    private final YzhGoodsClient yzhGoodsClient;

    private final Cache cache;

    @Operation(summary = "新增商品")
    @PostMapping(value = "/create")
    public ResultMessage<GoodsOperationDTO> save(@Valid @RequestBody SupplierGoodsOperationDTO supplierGoodsOperationDTO) {

        //参数自检
        supplierGoodsOperationDTO.paramsCheck();

        supplierGoodsService.addGoods(supplierGoodsOperationDTO);
        return ResultUtil.success();
    }

    @Operation(summary = "修改商品")
    @PutMapping(value = "/update/{goodsId}")
    public ResultMessage<GoodsOperationDTO> update(@RequestBody SupplierGoodsOperationDTO supplierGoodsOperationDTO, @PathVariable String goodsId) {


        supplierGoodsOperationDTO.paramsCheck();

        supplierGoodsOperationDTO.setGoodsId(goodsId);
        supplierGoodsService.editGoods(supplierGoodsOperationDTO);

        //todo 修改其他代发商品即订单问题处理
        return ResultUtil.success();
    }


    @Operation(summary = "获取批发规则")
    @GetMapping(value = "/wholesale/{goodsId}")
    public ResultMessage<Object> getWholesale(@PathVariable String goodsId) {
        return ResultUtil.data(goodsClient.getWholesale(goodsId));
    }

    @Operation(summary = "删除商品")
    @DeleteMapping(value = "/delete")
    public ResultMessage<Object> deleteGoods(@RequestParam List<String> goodsId) {
        supplierGoodsService.deleteGoods(goodsId);
        //todo 删除其他代发商品即订单问题处理
        return ResultUtil.success();
    }

    @Operation(summary = "云中鹤-商品初始化")
    @PostMapping(value = "/yzhGoodsInit")
    public ResultMessage<GoodsInitProgressDTO> yzhGoodsInit() {
        try {
            // 获取用户上下文信息
            plus.qdt.common.security.AuthUser currentUser = plus.qdt.common.security.context.UserContext.getCurrentExistUser();
            String userId = currentUser.getId();
            String scene = currentUser.getScene().name();
            String extendId = currentUser.getExtendId();

            // 生成任务ID
            String taskId = SnowFlake.getIdStr();

            // 初始化进度信息
            GoodsInitProgressDTO progress = new GoodsInitProgressDTO();
            progress.setTaskId(taskId);
            progress.setProgress(0);
            progress.setStatus(GoodsInitProgressDTO.Status.RUNNING.name());
            progress.setCurrentStep("正在启动云中鹤商品初始化...");
            progress.setStartTime(System.currentTimeMillis());
            progress.setProcessedCount(0);
            progress.setTotalCount(0);
            // 将进度信息存储到缓存中
            String progressKey = CachePrefix.GOODS_INIT_PROGRESS.getPrefix() + taskId;
            cache.put(progressKey, progress, 3600L); // 缓存1小时

            // 异步执行商品初始化（立即返回，不等待完成）
            CompletableFuture.runAsync(() -> executeGoodsInitAsync(taskId, progressKey, userId, scene, extendId));

            log.info("云中鹤商品初始化任务已启动，任务ID: {}, 用户ID: {}, 场景: {}, 扩展ID: {}", taskId, userId, scene, extendId);
            return ResultUtil.data(progress);

        } catch (Exception e) {
            log.error("启动云中鹤商品初始化失败: {}", e.getMessage(), e);
            return ResultUtil.error("启动商品初始化失败: " + e.getMessage());
        }
    }

    @Operation(summary = "查询云中鹤商品初始化进度")
    @GetMapping(value = "/yzhGoodsInit/progress/{taskId}")
    public ResultMessage<GoodsInitProgressDTO> getGoodsInitProgress(@PathVariable String taskId) {
        try {
            String progressKey = CachePrefix.GOODS_INIT_PROGRESS.getPrefix() + taskId;
            GoodsInitProgressDTO progress = (GoodsInitProgressDTO) cache.get(progressKey);

            if (progress == null) {
                return ResultUtil.error("任务不存在或已过期");
            }

            return ResultUtil.data(progress);

        } catch (Exception e) {
            log.error("查询商品初始化进度失败: {}", e.getMessage(), e);
            return ResultUtil.error("查询进度失败: " + e.getMessage());
        }
    }

    /**
     * 异步执行商品初始化
     *
     * @param taskId 任务ID
     * @param progressKey 进度缓存键
     * @param userId 用户ID
     * @param scene 场景
     * @param extendId 扩展ID
     */
    @Async
    public void executeGoodsInitAsync(String taskId, String progressKey, String userId, String scene, String extendId) {
        GoodsInitProgressDTO progress = null;
        try {
            // 获取当前进度
            progress = (GoodsInitProgressDTO) cache.get(progressKey);
            if (progress == null) {
                log.error("无法获取进度信息，任务ID: {}", taskId);
                return;
            }

            // 更新进度：准备调用服务
            updateProgress(progress, progressKey, 0, "正在调用云中鹤商品服务...", null);

            // 调用goods-service的商品初始化接口
            log.info("开始调用云中鹤商品初始化服务，任务ID: {}, 用户ID: {}, 场景: {}, 扩展ID: {}", taskId, userId, scene, extendId);
            ResultMessage<Object> result = yzhGoodsClient.goodsInit(taskId, userId, scene, extendId);

            if (result.isSuccess()) {
                // 成功完成
                updateProgress(progress, progressKey, 100, "商品初始化完成",
                              GoodsInitProgressDTO.Status.COMPLETED.name());
                progress.setEndTime(System.currentTimeMillis());
                progress.setResultMessage("商品初始化成功完成");

                log.info("云中鹤商品初始化成功完成，任务ID: {}", taskId);
            } else {
                // 调用失败（可能是业务失败或服务降级）
                String errorMsg = result.getMessage();
                boolean isServiceDown = errorMsg != null && errorMsg.contains("服务暂时不可用");

                updateProgress(progress, progressKey, 0, isServiceDown ? "服务暂时不可用" : "商品初始化失败",
                              GoodsInitProgressDTO.Status.FAILED.name());
                progress.setEndTime(System.currentTimeMillis());
                progress.setErrorMessage(errorMsg);
            }

        } catch (Exception e) {
            // 异常处理
            log.error("执行云中鹤商品初始化时发生异常，任务ID: {}, 异常: {}", taskId, e.getMessage(), e);

            if (progress != null) {
                updateProgress(progress, progressKey, 0, "商品初始化异常",
                              GoodsInitProgressDTO.Status.FAILED.name());
                progress.setEndTime(System.currentTimeMillis());
                progress.setErrorMessage("执行异常: " + e.getMessage());
            }
        } finally {
            // 更新最终进度到缓存
            if (progress != null) {
                cache.put(progressKey, progress, 3600L);
            }
        }
    }

    /**
     * 更新进度信息
     *
     * @param progress 进度对象
     * @param progressKey 缓存键
     * @param progressValue 进度值
     * @param currentStep 当前步骤
     * @param status 状态
     */
    private void updateProgress(GoodsInitProgressDTO progress, String progressKey,
                               Integer progressValue, String currentStep, String status) {
        progress.setProgress(progressValue);
        progress.setCurrentStep(currentStep);
        if (status != null) {
            progress.setStatus(status);
        }

        // 更新缓存
        cache.put(progressKey, progress, 3600L);

        log.debug("更新进度: {}% - {}", progressValue, currentStep);
    }

}
