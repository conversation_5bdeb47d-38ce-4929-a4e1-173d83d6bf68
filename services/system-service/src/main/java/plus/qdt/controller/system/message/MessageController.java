package plus.qdt.controller.system.message;

import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.PageVO;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.message.entity.dos.Message;
import plus.qdt.modules.message.entity.vos.MessageVO;
import plus.qdt.modules.message.service.MessageService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 消息发送管理接口
 *
 * <AUTHOR>
 * @since 2020-05-06 15:18:56
 */
@RestController
@Tag(name = "消息发送管理接口")
@RequestMapping("/system/message")
@RequiredArgsConstructor
public class MessageController {

    private final MessageService messageService;


    @GetMapping
    @Operation(summary = "多条件分页获取")
    public ResultMessage<Page<Message>> getByCondition(MessageVO messageVO,
                                                        PageVO pageVo) {
        return ResultUtil.data(messageService.getPage(messageVO, pageVo));
    }

    @PostMapping
    @Operation(summary = "发送消息")
    public ResultMessage<Boolean> sendMessage(Message message) {
        message.setType("SYSTEM");
        return ResultUtil.data(messageService.sendMessage(message));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除消息")
    
    public ResultMessage<Boolean> deleteMessage(@PathVariable String id) {

        return ResultUtil.data(messageService.deleteMessage(id));
    }

}
