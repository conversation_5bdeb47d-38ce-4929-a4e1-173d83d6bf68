package plus.qdt.modules.message.serviceimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.vo.PageVO;
import plus.qdt.modules.message.domain.vo.MyMessageVo;
import plus.qdt.modules.message.entity.dos.MemberMessage;
import plus.qdt.modules.message.entity.enums.MessageStatusEnum;
import plus.qdt.modules.message.entity.vos.MemberMessageQueryVO;
import plus.qdt.modules.message.entity.vos.MemberMessageVO;
import plus.qdt.modules.message.mapper.MemberMessageMapper;
import plus.qdt.modules.message.service.MemberMessageService;
import plus.qdt.mybatis.util.PageUtil;

import java.util.List;
import java.util.Objects;

/**
 * 会员接收消息业务层实现
 *
 * <AUTHOR>
 * @since 2020/11/17 3:48 下午
 */
@Service
@RequiredArgsConstructor
public class MemberMessageServiceImpl extends ServiceImpl<MemberMessageMapper, MemberMessage> implements MemberMessageService {

    @Override
    public void deleteAll() {
        LambdaQueryWrapper<MemberMessage> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MemberMessage::getMemberId, Objects.requireNonNull(UserContext.getCurrentUser()).getExtendId());
        lambdaQueryWrapper.eq(MemberMessage::getStatus, MessageStatusEnum.ALREADY_READY.name());
        this.remove(lambdaQueryWrapper);
    }

    @Override
    public void readAll() {
        LambdaUpdateWrapper<MemberMessage> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(MemberMessage::getMemberId, Objects.requireNonNull(UserContext.getCurrentUser()).getExtendId());
        lambdaUpdateWrapper.set(MemberMessage::getStatus, MessageStatusEnum.ALREADY_READY.name());
        this.update(lambdaUpdateWrapper);
    }

    @Override
    public void read(String messageId) {

        LambdaUpdateWrapper<MemberMessage> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(MemberMessage::getMemberId, Objects.requireNonNull(UserContext.getCurrentUser()).getExtendId());
        lambdaUpdateWrapper.eq(MemberMessage::getId, messageId);
        lambdaUpdateWrapper.set(MemberMessage::getStatus, MessageStatusEnum.ALREADY_READY.name());
        this.update(lambdaUpdateWrapper);
    }

    @Override
    public MemberMessageVO getPage(MemberMessageQueryVO memberMessageQueryVO, PageVO pageVO) {
        MemberMessageVO memberMessageVO = new MemberMessageVO();
        Page<MemberMessage> page = this.page(PageUtil.initPage(pageVO), memberMessageQueryVO.buildQueryWrapper());
        BeanUtils.copyProperties(page, memberMessageVO);
        memberMessageVO.setUnReadCount(this.unReadCount());
        //构建查询
        return memberMessageVO;
    }

    @Override
    public Boolean editStatus(String status, String messageId) {
        //查询消息是否存在
        MemberMessage memberMessage = this.getById(messageId);
        if (memberMessage != null) {
            memberMessage.setStatus(status);
            //执行修改
            return this.updateById(memberMessage);
        }
        return false;
    }


    @Override
    public Boolean deleteMessage(String messageId) {
        //查询消息是否存在
        MemberMessage memberMessage = this.getById(messageId);
        if (memberMessage != null) {
            //执行删除
            return this.removeById(memberMessage);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(List<MemberMessage> messages) {
        return saveBatch(messages);
    }

    @Override
    public Long unReadCount() {
        LambdaQueryWrapper<MemberMessage> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MemberMessage::getMemberId, Objects.requireNonNull(UserContext.getCurrentUser()).getExtendId());
        lambdaQueryWrapper.eq(MemberMessage::getStatus, MessageStatusEnum.UN_READY.name());
        return this.count(lambdaQueryWrapper);
    }

    @Override
    public MyMessageVo myMessage() {
        MyMessageVo messageVo = new MyMessageVo();
        Long service = this.baseMapper.selectCount(Wrappers.<MemberMessage>lambdaQuery()
                .eq(MemberMessage::getMemberId, UserContext.getCurrentExistUser().getId())
                .inSql(MemberMessage::getMessageId, "SELECT id FROM li_message WHERE type NOT IN ('ORDER_COMPLETED', 'STORE_REPLY')")
                .eq(MemberMessage::getStatus, MessageStatusEnum.UN_READY.name()));
        messageVo.setService(service);
        // 订单完成，去评价
        Long logistics = this.baseMapper.selectCount(Wrappers.<MemberMessage>lambdaQuery()
                .eq(MemberMessage::getMemberId, UserContext.getCurrentExistUser().getId())
                .inSql(MemberMessage::getMessageId, "SELECT id FROM li_message WHERE type='ORDER_COMPLETED'")
                .eq(MemberMessage::getStatus, MessageStatusEnum.UN_READY.name()));
        messageVo.setLogistics(logistics);
        // 未读互动聊天消息
        Long storeMessage = this.baseMapper.selectCount(Wrappers.<MemberMessage>lambdaQuery()
                .eq(MemberMessage::getMemberId, UserContext.getCurrentExistUser().getId())
                .inSql(MemberMessage::getMessageId, "SELECT id FROM li_message WHERE type='STORE_REPLY'")
                .eq(MemberMessage::getStatus, MessageStatusEnum.UN_READY.name()));
        messageVo.setInteraction(storeMessage);
        return messageVo;
    }
}