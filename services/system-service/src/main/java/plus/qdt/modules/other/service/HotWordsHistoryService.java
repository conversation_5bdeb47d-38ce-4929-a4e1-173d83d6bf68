package plus.qdt.modules.other.service;

import plus.qdt.modules.search.entity.dos.HotWordsHistory;
import plus.qdt.modules.search.entity.dto.HotWordsSearchParams;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;

/**
 * HotWordsService
 *
 * <AUTHOR>
 * @version v1.0
 * 2022-04-14 09:35
 */
public interface HotWordsHistoryService extends IService<HotWordsHistory> {

    /**
     * 热词统计
     *
     * @param hotWordsSearchParams 热词搜索参数
     * @return 热词统计结果
     */
    List<HotWordsHistory> statistics(HotWordsSearchParams hotWordsSearchParams);

    /**
     * 根据时间查询
     *
     * @param queryTime 查询时间
     * @return 热词统计结果
     */
    List<HotWordsHistory> queryByDay(Date queryTime);
}
