package plus.qdt.modules.message.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.vo.PageVO;
import plus.qdt.modules.message.domain.vo.MyMessageVo;
import plus.qdt.modules.message.entity.dos.MemberMessage;
import plus.qdt.modules.message.entity.enums.MessageStatusEnum;
import plus.qdt.modules.message.entity.vos.MemberMessageQueryVO;
import plus.qdt.modules.message.entity.vos.MemberMessageVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 会员消息发送业务层
 *
 * <AUTHOR>
 * @since 2020/11/17 3:44 下午
 */
public interface MemberMessageService extends IService<MemberMessage> {


    /**
     * 删除所有消息
     */
    void deleteAll();

    /**
     * 读取所有消息
     */
    void readAll();

    /**
     * 读取消息
     */
    void read(String messageId);

    /**
     * 会员消息查询接口
     *
     * @param memberMessageQueryVO 会员查询条件
     * @param pageVO               分页条件
     * @return 会员消息分页
     */
    MemberMessageVO getPage(MemberMessageQueryVO memberMessageQueryVO, PageVO pageVO);

    /**
     * 修改会员消息状态
     *
     * @param status    状态
     * @param messageId 消息id
     * @return 操作状态
     */
    Boolean editStatus(String status, String messageId);

    /**
     * 删除消息
     *
     * @param messageId 消息id
     * @return 操作状态
     */
    Boolean deleteMessage(String messageId);


    /**
     * 保存消息信息
     * @param messages 消息
     * @return {@link boolean}
     */
    boolean save(List<MemberMessage> messages);


    /**
     * 维度消息数量
     * @return 未读消息数量
     */
    Long unReadCount();

    /**
     * 获取我的消息数量
     * @return {@link MyMessageVo}
     * <AUTHOR>
     */
    MyMessageVo myMessage();

    /**
     * 获取我的消息列表
     * @param type 消息类型
     * @param messageStatusEnum 消息状态
     * @return {@link List} {@link MemberMessage}
     * <AUTHOR>
     */
    default List<MemberMessage> messageList(String type, MessageStatusEnum messageStatusEnum) {
        LambdaQueryWrapper<MemberMessage> lqw = new LambdaQueryWrapper<>();
        lqw.eq(MemberMessage::getMemberId, UserContext.getCurrentExistUser().getId());
        lqw.orderByDesc(MemberMessage::getCreateTime);
        if ("SERVICE_NOTICE".equals(type)) {
            lqw.inSql(MemberMessage::getMessageId, "SELECT id FROM li_message WHERE type NOT IN ('ORDER_COMPLETED', 'STORE_REPLY')");
        } else {
            lqw.inSql(MemberMessage::getMessageId, String.format("SELECT id FROM li_message WHERE type='%s'", type));
        }
        lqw.eq(MemberMessage::getStatus, messageStatusEnum.name());
        return this.list(lqw);
    }
}