package plus.qdt.modules.system.serviceimpl;

import cn.hutool.json.JSONUtil;
import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.event.TransactionCommitSendMQEvent;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.utils.ValidateParamsUtil;
import plus.qdt.exchange.AmqpExchangeProperties;
import plus.qdt.modules.distribution.entity.enums.DistributionModelEnum;
import plus.qdt.modules.goods.client.GoodsClient;
import plus.qdt.modules.goods.entity.dos.GoodsSku;
import plus.qdt.modules.goods.entity.dto.GoodsSearchParams;
import plus.qdt.modules.goods.entity.enums.GoodsAuthEnum;
import plus.qdt.modules.goods.entity.enums.GoodsMarketEnum;
import plus.qdt.modules.goods.entity.enums.SalesModeEnum;
import plus.qdt.modules.system.entity.dos.Setting;
import plus.qdt.modules.system.entity.dto.DistributionSetting;
import plus.qdt.modules.system.entity.enums.SettingEnum;
import plus.qdt.modules.system.mapper.SettingMapper;
import plus.qdt.modules.system.service.SettingService;
import plus.qdt.routing.GoodsRoutingKey;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 配置业务层实现
 *
 * <AUTHOR>
 * @since 2020/11/17 3:52 下午
 */
@Service
@RequiredArgsConstructor
public class SettingServiceImpl extends ServiceImpl<SettingMapper, Setting> implements SettingService {

    private final AmqpExchangeProperties amqpExchangeProperties;

    private final ApplicationEventPublisher applicationEventPublisher;

    private final GoodsClient goodsClient;

    @Override
    public Setting get(String key) {
        return this.getById(key);
    }

    @Override
    @Transactional
    public boolean saveUpdate(Setting setting) {
        // 当分销设置被修改，发送消息更新分销商品（修改佣金模式、佣金比例需要重新计算分销商品的一二级佣金金额）
        if (SettingEnum.DISTRIBUTION_SETTING.name().equals(setting.getId())) {
            // 判断分销设置佣金模式
            DistributionSetting distributionSetting = JSONUtil.toBean(setting.getSettingValue(), DistributionSetting.class);
            // 限制分销佣金比例 0.1%-20% 区域
            if (!ValidateParamsUtil.isValidDoubleValue(distributionSetting.getFirstCommission(), 0.1, 20)
                || !ValidateParamsUtil.isValidDoubleValue(distributionSetting.getSecondaryCommission(), 0.1, 20)) {
                throw new ServiceException(ResultCode.DISTRIBUTION_COMMISSION_RATE_ERROR);
            }

            List<String> skuIds = new ArrayList<>();
            // 平台承担模式下，需要同步所有审核通过切上架的零售商品至分销商品库
            if (distributionSetting.getCommissionModel().equals(DistributionModelEnum.PLATFORM.name())) {
                List<GoodsSku> goodsSkuList = goodsClient.getGoodsSkuByList(GoodsSearchParams.builder()
                        .marketEnable(GoodsMarketEnum.UPPER.name())
                        .authFlag(GoodsAuthEnum.PASS.name())
                        .deleteFlag(false)
                        .salesModel(SalesModeEnum.RETAIL.name()).build());
                skuIds = goodsSkuList.stream()
                        .map(GoodsSku::getId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
            applicationEventPublisher.publishEvent(
                    TransactionCommitSendMQEvent.builder().source("平台承担佣金-同步分销商品").exchange(amqpExchangeProperties.getGoods())
                            .routingKey(GoodsRoutingKey.SYNC_PLATFORM_DISTRIBUTION_GOODS).message(skuIds).build());
        }
        return this.saveOrUpdate(setting);
    }
}