package plus.qdt.controller.promotion;

import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.security.AuthUser;
import plus.qdt.common.security.OperationalJudgment;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.security.enums.AuthUserFieldEnum;
import plus.qdt.common.security.enums.ObjectFieldEnum;
import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.PageVO;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.order.cart.entity.vo.FullDiscountVO;
import plus.qdt.modules.promotion.entity.dos.FullDiscount;
import plus.qdt.modules.promotion.entity.dto.search.FullDiscountSearchParams;
import plus.qdt.modules.promotion.service.FullDiscountService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.Objects;

/**
 * 满额活动接口
 *
 * <AUTHOR>
 * @since 2021/1/12
 **/
@RestController
@Tag(name = "满额活动接口")
@RequestMapping("/promotion/full-discount")
@RequiredArgsConstructor
public class FullDiscountController {


    private final FullDiscountService fullDiscountService;

    @Operation(summary = "获取满优惠列表")
    @GetMapping
    public ResultMessage<Page<FullDiscount>> getCouponList(FullDiscountSearchParams searchParams, PageVO page) {
        AuthUser currentUser = Objects.requireNonNull(UserContext.getCurrentUser());
        if (SceneEnums.MANAGER.equals(currentUser.getScene()) || SceneEnums.SYSTEM.equals(currentUser.getScene())) {
            return ResultUtil.data(fullDiscountService.pageFindAll(searchParams, page));
        }
        searchParams.setStoreId(currentUser.getExtendId());
        return ResultUtil.data(fullDiscountService.pageFindAll(searchParams, page));
    }

    @Operation(summary = "获取满优惠详情")
    @GetMapping("/{id}")
    public ResultMessage<FullDiscountVO> getCouponDetail(@PathVariable String id) {
        OperationalJudgment.judgment(fullDiscountService.getById(id), ObjectFieldEnum.STORE_ID, AuthUserFieldEnum.EXTEND_ID);
        return ResultUtil.data(fullDiscountService.getFullDiscount(id));
    }

    @Operation(summary = "修改满额活动状态")
    @PutMapping("/status/{id}")
    public ResultMessage<Object> updateCouponStatus(@PathVariable String id, Long startTime, Long endTime) {
        OperationalJudgment.judgment(fullDiscountService.getById(id), ObjectFieldEnum.STORE_ID, AuthUserFieldEnum.EXTEND_ID);
        if (fullDiscountService.updateStatus(Collections.singletonList(id), startTime, endTime)) {
            return ResultUtil.success();
        }
        return ResultUtil.error();
    }

    @Operation(summary = "新增满优惠活动")
    @PostMapping(consumes = "application/json", produces = "application/json")
    public ResultMessage<FullDiscount> addFullDiscount(@RequestBody FullDiscountVO fullDiscountVO) {
        AuthUser currentUser = Objects.requireNonNull(UserContext.getCurrentUser());
        fullDiscountVO.setStoreId(currentUser.getExtendId());
        fullDiscountVO.setStoreName(currentUser.getExtendName());
        if (!fullDiscountService.savePromotions(fullDiscountVO)) {
            return ResultUtil.error(ResultCode.PINTUAN_ADD_ERROR);
        }
        return ResultUtil.data(fullDiscountVO);
    }

    @Operation(summary = "修改满优惠活动")
    @PutMapping(consumes = "application/json", produces = "application/json")
    public ResultMessage<String> editFullDiscount(@RequestBody FullDiscountVO fullDiscountVO) {
        OperationalJudgment.judgment(fullDiscountService.getById(fullDiscountVO.getId()), ObjectFieldEnum.STORE_ID, AuthUserFieldEnum.EXTEND_ID);
        AuthUser currentUser = Objects.requireNonNull(UserContext.getCurrentUser());
        fullDiscountVO.setStoreId(currentUser.getExtendId());
        fullDiscountVO.setStoreName(currentUser.getExtendName());
        if (!fullDiscountService.updatePromotions(fullDiscountVO)) {
            return ResultUtil.error(ResultCode.PINTUAN_EDIT_ERROR);
        }
        return ResultUtil.success();
    }

    @Operation(summary = "删除满优惠活动")
    @DeleteMapping("/{id}")
    public ResultMessage<String> deleteFullDiscount(@PathVariable String id) {
        OperationalJudgment.judgment(fullDiscountService.getById(id), ObjectFieldEnum.STORE_ID, AuthUserFieldEnum.EXTEND_ID);
        fullDiscountService.removePromotions(Collections.singletonList(id));
        return ResultUtil.success();
    }


}
