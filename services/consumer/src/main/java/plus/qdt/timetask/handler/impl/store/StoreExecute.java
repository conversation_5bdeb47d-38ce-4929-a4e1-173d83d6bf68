package plus.qdt.timetask.handler.impl.store;

import plus.qdt.modules.goods.client.GoodsClient;
import plus.qdt.modules.store.client.StoreClient;
import plus.qdt.modules.store.entity.dos.Store;
import plus.qdt.modules.store.entity.dto.StoreSearchParams;
import plus.qdt.modules.store.entity.enums.StoreStatusEnum;
import plus.qdt.timetask.handler.EveryDayExecute;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 店铺信息更新
 *
 * <AUTHOR>
 * @since 2021/3/15 5:30 下午
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class StoreExecute implements EveryDayExecute {
    /**
     * 店铺
     */
    private final StoreClient storeClient;

    private final GoodsClient goodsClient;

    @Override
    public void execute() {
        StoreSearchParams storeSearchParams = new StoreSearchParams();
        storeSearchParams.setStoreStatus(StoreStatusEnum.OPEN.name());
        //获取所有开启的店铺
        List<Store> storeList = storeClient.list(storeSearchParams);

        for (Store store : storeList) {
            try {
                Long num = goodsClient.countSkuNum(store.getId());
                storeClient.updateStoreGoodsNum(store.getId(), num);
            } catch (Exception e) {
                log.error("店铺id为{},更新商品数量失败", store.getId(), e);
            }
        }


    }
}
