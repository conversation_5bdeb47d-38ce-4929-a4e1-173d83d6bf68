package plus.qdt.listener;

import cn.hutool.json.JSONUtil;
import plus.qdt.common.security.AuthUser;
import plus.qdt.event.*;
import plus.qdt.modules.member.client.MemberSignClient;
import plus.qdt.modules.member.entity.dos.MemberSign;
import plus.qdt.modules.member.entity.dos.User;
import plus.qdt.modules.payment.entity.dto.UserPointUpdateDTO;
import plus.qdt.modules.payment.entity.dto.UserWithdrawalMessage;
import plus.qdt.modules.store.entity.dos.Store;
import plus.qdt.routing.UserRoutingKey;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户消息
 *
 * <AUTHOR>
 * @since 2020/12/9
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class UserMessageListener {

    /**
     * 用户签到
     */
    private final MemberSignClient memberSignClient;
    /**
     * 用户积分变化
     */
    private final List<UserPointChangeEvent> userPointChangeEvents;
    /**
     * 用户提现
     */
    private final List<UserWithdrawalEvent> userWithdrawalEvents;
    /**
     * 用户注册
     */
    private final List<UserRegisterEvent> userRegisterEvents;

    /**
     * 用户登录
     */
    private final List<UserLoginEvent> userLoginEvents;

    /**
     * 店铺信息
     */
    private final List<StoreEvent> storeEvents;

    private final List<UserInfoEvent> userInfoEvents;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "${lili.amqp.user}" + "_" + UserRoutingKey.USER_REGISTER),
            exchange = @Exchange(value = "${lili.amqp.user}"),
            key = UserRoutingKey.USER_REGISTER))
    public void userRegister(String userJson) {
        User user = JSONUtil.toBean(userJson, User.class);
        for (UserRegisterEvent userRegisterEvent : userRegisterEvents) {
            try {
                userRegisterEvent.userRegister(user);
            } catch (Exception e) {
                log.error("用户{},在{}业务中，状态修改事件执行异常:{}",
                        user,
                        userRegisterEvent.getClass().getName(),
                        e);
            }
        }
    }

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "${lili.amqp.user}" + "_" + UserRoutingKey.USER_LOGIN),
            exchange = @Exchange(value = "${lili.amqp.user}"),
            key = UserRoutingKey.USER_LOGIN))
    public void userLogin(String authUserJson) {
        AuthUser authUser = JSONUtil.toBean(authUserJson, AuthUser.class);

        for (UserLoginEvent userLoginEvent : userLoginEvents) {
            try {
                userLoginEvent.userLogin(authUser);
            } catch (Exception e) {
                log.error("用户{},在{}业务中，状态修改事件执行异常",
                        authUser,
                        userLoginEvent.getClass().getName(),
                        e);
            }
        }
    }


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "${lili.amqp.user}" + "_" + UserRoutingKey.USER_SING),
            exchange = @Exchange(value = "${lili.amqp.user}"),
            key = UserRoutingKey.USER_SING))
    public void userSign(String memberSignJson) {
        MemberSign memberSign = JSONUtil.toBean(memberSignJson, MemberSign.class);
        memberSignClient.memberSignSendPoint(memberSign.getMemberId(), memberSign.getSignDay());
    }


    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "${lili.amqp.user}" + "_" + UserRoutingKey.USER_POINT_CHANGE),
            exchange = @Exchange(value = "${lili.amqp.user}"),
            key = UserRoutingKey.USER_POINT_CHANGE))
    public void userPointChange(String userPointUpdateDTOJson) {
        UserPointUpdateDTO userPointUpdateDTO = JSONUtil.toBean(userPointUpdateDTOJson, UserPointUpdateDTO.class);
        for (UserPointChangeEvent userPointChangeEvent : userPointChangeEvents) {
            try {
                userPointChangeEvent.userPointChange(userPointUpdateDTO);
            } catch (Exception e) {
                log.error("用户{},在{}业务中，状态修改事件执行异常",
                        userPointUpdateDTO,
                        userPointChangeEvent.getClass().getName(),
                        e);
            }
        }
    }

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "${lili.amqp.user}" + "_" + UserRoutingKey.USER_WITHDRAWAL),
            exchange = @Exchange(value = "${lili.amqp.user}"),
            key = UserRoutingKey.USER_WITHDRAWAL))
    public void userWithdrawal(String userWithdrawalMessageJson) {
        UserWithdrawalMessage userWithdrawalMessage = JSONUtil.toBean(userWithdrawalMessageJson, UserWithdrawalMessage.class);

        for (UserWithdrawalEvent userWithdrawalEvent : userWithdrawalEvents) {
            try {
                userWithdrawalEvent.userWithdrawal(userWithdrawalMessage);
            } catch (Exception e) {
                log.error("用户{},在{}业务中，提现事件执行异常",
                        userWithdrawalMessage,
                        userWithdrawalEvent.getClass().getName(),
                        e);
            }
        }
    }

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "${lili.amqp.user}" + "_" + UserRoutingKey.STORE_UPDATE),
            exchange = @Exchange(value = "${lili.amqp.user}"),
            key = UserRoutingKey.STORE_UPDATE))
    public void updateStoreInfo(String storeInfoJson) {
        Store store = JSONUtil.toBean(storeInfoJson, Store.class);
        for (StoreEvent storeEvent : storeEvents) {
            try {
                storeEvent.updateStoreInfo(store);
            }catch (Exception e) {
                log.error("用户{},在{}业务中，修改事件执行异常",
                        store,
                        storeEvent.getClass().getName(),
                        e);
            }
        }

    }

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "${lili.amqp.user}" + "_" + UserRoutingKey.USER_UPDATE),
            exchange = @Exchange(value = "${lili.amqp.user}"),
            key = UserRoutingKey.USER_UPDATE))
    public void updateUserInfo(String userInfoJson) {
        User user = JSONUtil.toBean(userInfoJson, User.class);
        for (UserInfoEvent userInfoEvent : userInfoEvents) {
            try {
                userInfoEvent.updateUserInfo(user);
            }catch (Exception e) {
                log.error("用户{},在{}业务中，修改事件执行异常",
                        user,
                        userInfoEvent.getClass().getName(),
                        e);
            }
        }

    }

}
