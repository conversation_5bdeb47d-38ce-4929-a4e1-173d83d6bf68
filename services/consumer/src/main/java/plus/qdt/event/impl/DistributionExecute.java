package plus.qdt.event.impl;

import plus.qdt.event.AfterSaleStatusChangeEvent;
import plus.qdt.event.OrderStatusChangeEvent;
import plus.qdt.modules.distribution.client.DistributionOrderClient;
import plus.qdt.modules.order.aftersale.entity.dto.AfterSaleMessageDTO;
import plus.qdt.modules.order.order.entity.dto.OrderMessage;
import plus.qdt.modules.order.trade.entity.enums.AfterSaleStatusEnum;
import plus.qdt.modules.system.client.SettingClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 分销订单入库
 *
 * <AUTHOR>
 * @since 2020-07-03 11:20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DistributionExecute implements OrderStatusChangeEvent, AfterSaleStatusChangeEvent {

    private final DistributionOrderClient distributionOrderClient;

    @Override
    public void afterSaleStatusChange(AfterSaleMessageDTO afterSale) {
        //售后状态变更，处理分销订单
        if (afterSale.getServiceStatus().equals(AfterSaleStatusEnum.COMPLETE.name())) {
            distributionOrderClient.refundOrder(afterSale);
        }
    }

    @Override
    public void orderChange(OrderMessage orderMessage) {
        switch (orderMessage.getNewStatus()) {
            //订单带校验/订单代发货/待自提，则记录分销信息
            case TAKE:
            case STAY_PICKED_UP:
            case UNDELIVERED: {
                //记录分销订单
                distributionOrderClient.calculationDistribution(orderMessage.getOrderSn());
                break;
            }
            case CANCELLED: {
                //修改分销订单状态
                distributionOrderClient.cancelOrder(orderMessage.getOrderSn());
                break;
            }
            case COMPLETED: {
                // 订单完成后，修改分销订单状态
                distributionOrderClient.updateDistributionOrderByOrderSn(orderMessage.getOrderSn());
                break;
            }
            default: {
                break;
            }
        }
    }
}
