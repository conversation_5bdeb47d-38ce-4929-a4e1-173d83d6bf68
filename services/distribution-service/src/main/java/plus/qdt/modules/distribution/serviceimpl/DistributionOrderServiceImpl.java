package plus.qdt.modules.distribution.serviceimpl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONUtil;
import plus.qdt.common.enums.PromotionTypeEnum;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.utils.CurrencyUtil;
import plus.qdt.common.utils.DateUtil;
import plus.qdt.modules.distribution.entity.dos.Distribution;
import plus.qdt.modules.distribution.entity.dos.DistributionBind;
import plus.qdt.modules.distribution.entity.dos.DistributionOrder;
import plus.qdt.modules.distribution.entity.dto.DistributionDTO;
import plus.qdt.modules.distribution.entity.enums.DistributionOrderStatusEnum;
import plus.qdt.modules.distribution.entity.vos.DistributionOrderSearchParams;
import plus.qdt.modules.distribution.mapper.DistributionOrderMapper;
import plus.qdt.modules.distribution.service.DistributionBindService;
import plus.qdt.modules.distribution.service.DistributionOrderService;
import plus.qdt.modules.distribution.service.DistributionService;
import plus.qdt.modules.member.client.UserClient;
import plus.qdt.modules.order.aftersale.entity.dos.AfterSale;
import plus.qdt.modules.order.order.client.DistributionOrderClient;
import plus.qdt.modules.order.order.client.OrderClient;
import plus.qdt.modules.order.order.client.OrderFlowClient;
import plus.qdt.modules.order.order.client.OrderItemClient;
import plus.qdt.modules.order.order.entity.dos.Order;
import plus.qdt.modules.order.order.entity.dos.OrderFlow;
import plus.qdt.modules.order.order.entity.dos.OrderItem;
import plus.qdt.modules.order.order.entity.dos.OrderItemFlow;
import plus.qdt.modules.order.order.entity.dto.OrderFlowQueryDTO;
import plus.qdt.modules.order.order.entity.enums.OrderItemAfterSaleStatusEnum;
import plus.qdt.modules.order.order.entity.enums.PayStatusEnum;
import plus.qdt.modules.system.client.SettingClient;
import plus.qdt.modules.system.entity.dos.Setting;
import plus.qdt.modules.system.entity.dto.DistributionSetting;
import plus.qdt.modules.system.entity.enums.SettingEnum;
import plus.qdt.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 分销订单接口实现
 *
 * <AUTHOR>
 * @since 2020-03-14 23:04:56
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DistributionOrderServiceImpl extends ServiceImpl<DistributionOrderMapper, DistributionOrder> implements DistributionOrderService {

    private final DistributionOrderClient distributionOrderClient;
    private final OrderClient orderClient;
    private final OrderItemClient orderItemClient;
    private final OrderFlowClient orderFlowClient;
    private final DistributionService distributionService;
    private final DistributionBindService distributionBindService;
    private final SettingClient settingClient;
    private final UserClient userClient;

    @Override
    public IPage<DistributionOrder> getDistributionOrderPage(DistributionOrderSearchParams distributionOrderSearchParams) {
        return this.page(PageUtil.initPage(distributionOrderSearchParams), distributionOrderSearchParams.queryWrapper());

    }

    /**
     * 1.查看订单是否为分销订单
     * 2.查看店铺流水计算分销总佣金
     * 3.修改分销员的分销总金额、冻结金额
     *
     * @param orderSn 订单编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void calculationDistribution(String orderSn) {

        //根据订单编号获取订单数据
        Order order = orderClient.getBySn(orderSn);

        //判断是否为分销订单，如果为分销订单则获取分销佣金
        if (order.getDistributionId() != null) {
            //根据订单编号获取有分销金额的店铺流水记录
            List<OrderFlow> orderFlowList = orderFlowClient.listStoreFlow(OrderFlowQueryDTO.builder().justDistribution(true).orderSn(orderSn).build());

            double firstRebate = 0.0;
            double secondaryRebate = 0.0;
            Setting setting = settingClient.get(SettingEnum.DISTRIBUTION_SETTING.name());
            DistributionSetting distributionSetting = JSONUtil.toBean(setting.getSettingValue(), DistributionSetting.class);

            //循环店铺流水记录判断是否包含分销商品
            for (OrderFlow orderFlow : orderFlowList) {
                List<OrderItemFlow> orderItemFlowList = orderFlowClient.listOrderItemFlow(orderFlow.getOrderSn());
                for (OrderItemFlow orderItemFlow : orderItemFlowList) {
                    firstRebate = orderItemFlow.getFirstRebate();

                    DistributionOrder distributionOrder = new DistributionOrder(orderFlow, orderItemFlow);
                    distributionOrder.setDistributionId(order.getDistributionId());
                    distributionOrder.setDistributionName(order.getDistributionName());

                    //判断是否是二级分销
                    if ("2".equals(distributionSetting.getLevel())) {
                        //判断是否存在上级分销员
                        DistributionBind distributionBind = distributionBindService.getUpDistributionBind(order.getDistributionId());
                        if (distributionBind != null) {
                            Distribution secondaryDistribution = distributionService.getById(distributionBind.getDistributionId());
                            distributionOrder.setSecondaryDistributionId(secondaryDistribution.getId());
                            distributionOrder.setSecondaryDistributionName(secondaryDistribution.getMemberName());
                            secondaryRebate = orderItemFlow.getSecondaryRebate();
                        }
                    }
                    //添加分销订单
                    this.save(distributionOrder);

                    //记录会员的分销总额
                    if (firstRebate != 0.0) {
                        distributionService.addRebate(firstRebate, distributionOrder.getDistributionId(), orderItemFlow.getFlowPrice());
                    }
                    if (secondaryRebate != 0.0) {
                        distributionService.addRebate(secondaryRebate, distributionOrder.getSecondaryDistributionId(), orderItemFlow.getFlowPrice());
                    }
                    // 记录分销商客户数据
                    distributionBindService.addRebate(firstRebate, order.getBuyerId(), orderItemFlow.getFlowPrice());

                }
            }
        }

    }

    /**
     * 1.获取订单判断是否为已付款的分销订单
     * 2.查看店铺流水记录分销佣金
     * 3.修改分销员的分销总金额、可提现金额
     *
     * @param orderSn 订单编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder(String orderSn) {
        //根据订单编号获取订单数据
        Order order = orderClient.getBySn(orderSn);

        //判断是否为已付款的分销订单，则获取分销佣金
        if (order.getDistributionId() != null && order.getPayStatus().equals(PayStatusEnum.PAID.name())) {

            //根据订单编号获取有分销金额的店铺流水记录
            List<DistributionOrder> distributionOrderList = this.list(new LambdaQueryWrapper<DistributionOrder>()
                    .eq(DistributionOrder::getOrderSn, orderSn));

            //如果没有分销定单，则直接返回
            if (distributionOrderList.isEmpty()) {
                return;
            }
            //包含分销商品则进行记录分销订单、计算分销总额
            for (DistributionOrder distributionOrder : distributionOrderList) {

                //修改分销订单的状态
                this.update(new LambdaUpdateWrapper<DistributionOrder>().eq(DistributionOrder::getId, distributionOrder.getId())
                        .set(DistributionOrder::getDistributionOrderStatus, DistributionOrderStatusEnum.CANCEL.name())
                        .set(DistributionOrder::getRefundNum, distributionOrder.getNum()));


                //如果包含分销商品则记录会员的分销总额
                if (distributionOrder.getFirstRebate() != 0.0) {
                    distributionService.subRebate(distributionOrder.getFirstRebate(), distributionOrder.getDistributionId(), distributionOrder.getSellBackRebate());
                }
                if (distributionOrder.getSecondaryRebate() != 0.0) {
                    distributionService.subRebate(distributionOrder.getSecondaryRebate(), distributionOrder.getSecondaryDistributionId(),
                            distributionOrder.getSellBackRebate());
                }
            }
        }
    }

    @Override
    public void refundOrder(AfterSale afterSale) {
        //判断是否为分销订单
        OrderItemFlow refundItemFlow = orderFlowClient.getOrderItemFlow(afterSale.getOrderItemSn(), afterSale.getSkuId());
        if (refundItemFlow != null) {
            //获取收款分销订单
            DistributionOrder distributionOrder = this.getOne(new LambdaQueryWrapper<DistributionOrder>()
                    .eq(DistributionOrder::getOrderItemSn, afterSale.getOrderItemSn()));
            //分销订单不存在，则直接返回
            if (distributionOrder == null) {
                return;
            }
            if (distributionOrder.getSellBackRebate() == null) {
                distributionOrder.setSellBackRebate(refundItemFlow.getFirstRebate());
            } else {
                distributionOrder.setSellBackRebate(CurrencyUtil.add(distributionOrder.getSellBackRebate(), refundItemFlow.getFirstRebate()));
            }

            distributionOrder.setFirstRebate(CurrencyUtil.sub(distributionOrder.getFirstRebate(), refundItemFlow.getFirstRebate()));
            if (distributionOrder.getFirstRebate() == 0) {
                distributionOrder.setDistributionOrderStatus(DistributionOrderStatusEnum.REFUND.name());
            }
            distributionOrder.setRefundNum(distributionOrder.getRefundNum() + afterSale.getNum());
            this.updateById(distributionOrder);

            //            修改分销员提成金额
            if (refundItemFlow.getFirstRebate() != 0.0) {
                distributionService.subRebate(refundItemFlow.getFirstRebate(), distributionOrder.getDistributionId(), refundItemFlow.getFlowPrice());
            }
            if (refundItemFlow.getSecondaryRebate() != 0.0) {
                distributionService.subRebate(refundItemFlow.getSecondaryRebate(), distributionOrder.getSecondaryDistributionId(), refundItemFlow.getFlowPrice());
            }
        }
    }

    @Override
    public void updateDistributionOrderStatus(OrderItemFlow orderItemFlow) {

        DistributionOrder distributionOrder = this.getOne(new LambdaQueryWrapper<DistributionOrder>()
                .eq(DistributionOrder::getOrderItemSn, orderItemFlow.getOrderItemSn()));

        //添加分销员佣金
        //解冻金额= 订单佣金-退款佣金
        if (distributionOrder.getFirstRebate() != 0.0) {
            distributionService.addCanRebate(distributionOrder.getFirstRebate(), distributionOrder.getDistributionId());
        }
        if (distributionOrder.getSecondaryRebate() != 0.0) {
            distributionService.addCanRebate(distributionOrder.getSecondaryRebate(), distributionOrder.getSecondaryDistributionId());
        }

        //分销
        // 订单完成
        this.update(new LambdaUpdateWrapper<DistributionOrder>()
                .eq(DistributionOrder::getId, distributionOrder.getDistributionId())
                .set(DistributionOrder::getDistributionOrderStatus, DistributionOrderStatusEnum.WAIT_CASH.name()));


    }

    @Override
    public void updateDistributionOrderStatus(String orderSn) {
        List<OrderItem> orderItemList = orderItemClient.getByOrderSn(orderSn);
        orderItemList.forEach(orderItem -> {
            DistributionOrder distributionOrder = this.getOne(new LambdaQueryWrapper<DistributionOrder>().eq(DistributionOrder::getOrderItemSn, orderItem.getSn()), false);
            // 如果订单状态已完成，并且不可进行售后则完成分销结算
            if (CharSequenceUtil.equalsAny(orderItem.getAfterSaleStatus(), OrderItemAfterSaleStatusEnum.EXPIRED.name(), OrderItemAfterSaleStatusEnum.ALREADY_APPLIED.name())
                    && !PromotionTypeEnum.isCanAfterSale(orderItem.getPromotionType())) {
                this.settlementDistributionOrder(distributionOrder);
            } else {
                //订单可进行售后，则修改分销订单状态未待结算，解冻佣金
                distributionOrder.setDistributionOrderStatus(DistributionOrderStatusEnum.WAIT_BILL.name());
                this.updateById(distributionOrder);
            }
        });
    }

    @Override
    public void settlementDistributionOrder(DistributionOrder distributionOrder) {
        //解冻金额= 订单佣金-退款佣金
        if (distributionOrder.getFirstRebate() != 0.0) {
            distributionService.addCanRebate(distributionOrder.getFirstRebate(), distributionOrder.getDistributionId());
        }
        if (distributionOrder.getSecondaryRebate() != 0.0) {
            distributionService.addCanRebate(distributionOrder.getSecondaryRebate(), distributionOrder.getSecondaryDistributionId());
        }
        distributionOrder.setDistributionOrderStatus(DistributionOrderStatusEnum.WAIT_CASH.name());
        this.updateById(distributionOrder);
    }

    @Override
    public DistributionDTO statisticsDistributionInfo() {
        DistributionDTO distributionDTO = distributionOrderClient.distributionStatistics();
        distributionDTO.setBaseProperties(distributionService.getDistribution(),
                userClient.getById(UserContext.getCurrentUser().getId()));
        distributionBindService.statistics(distributionDTO);
        distributionService.statistics(distributionDTO);
        return distributionDTO;
    }

    @Override
    public List<DistributionOrder> getPendingDistributionOrderList(String storeId) {
        return this.list(new LambdaQueryWrapper<DistributionOrder>()
                .eq(DistributionOrder::getDistributionOrderStatus, DistributionOrderStatusEnum.WAIT_BILL.name())
                .eq(CharSequenceUtil.isNotEmpty(storeId), DistributionOrder::getStoreId, storeId));
    }

}