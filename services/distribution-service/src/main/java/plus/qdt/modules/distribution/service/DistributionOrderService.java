package plus.qdt.modules.distribution.service;

import plus.qdt.modules.distribution.entity.dos.DistributionOrder;
import plus.qdt.modules.distribution.entity.dto.DistributionDTO;
import plus.qdt.modules.distribution.entity.vos.DistributionOrderSearchParams;
import plus.qdt.modules.order.aftersale.entity.dos.AfterSale;
import plus.qdt.modules.order.order.entity.dos.OrderItemFlow;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;


/**
 * 分销订单业务层
 *
 * <AUTHOR>
 * @since 2020-03-15 10:46:33
 */
public interface DistributionOrderService extends IService<DistributionOrder> {

    /**
     * 获取分销订单分页
     *
     * @param distributionOrderSearchParams 分销订单搜索参数
     * @return 分销订单分页
     */
    IPage<DistributionOrder> getDistributionOrderPage(DistributionOrderSearchParams distributionOrderSearchParams);

    /**
     * 支付订单
     * 记录分销订单
     *
     * @param orderSn 订单编号
     */
    void calculationDistribution(String orderSn);

    /**
     * 取消订单
     * 记录分销订单
     *
     * @param orderSn 订单编号
     */
    void cancelOrder(String orderSn);

    /**
     * 订单退款
     * 记录分销订单
     *
     * @param afterSale 售后
     */
    void refundOrder(AfterSale afterSale);

    /**
     * 分销订单状态修改
     */
    void updateDistributionOrderStatus(OrderItemFlow orderItemFlow);

    /**
     * 完善分销订单状态
     * @param orderSn
     */
    void updateDistributionOrderStatus(String orderSn);

    /**
     * 统计今日份分销员信息
     * @return
     */
    DistributionDTO statisticsDistributionInfo();

    /**
     * 获取待结算的分销订单列表
     * @return
     */
    List<DistributionOrder> getPendingDistributionOrderList(String storeId);

    /**
     * 结算分销订单
     * @param distributionOrder
     */
    void settlementDistributionOrder(DistributionOrder distributionOrder);
}