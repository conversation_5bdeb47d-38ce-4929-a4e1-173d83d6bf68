package plus.qdt.modules.distribution.mapper;

import plus.qdt.modules.distribution.entity.dos.Distribution;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Map;


/**
 * 分销员数据处理层
 *
 * <AUTHOR>
 * @since 2020-03-14 23:04:56
 */
public interface DistributionMapper extends BaseMapper<Distribution> {

    /**
     * 添加分销金额
     *
     * @param commissionFrozen 分销金额
     * @param distributionId   分销员ID
     */
    @Update("UPDATE li_distribution set rebate_total=(rebate_total+#{rebate_total}) " +
            ", distribution_order_count=(distribution_order_count+1) WHERE id = #{distributionId}")
    void addCommission(Double commissionFrozen, String distributionId);

    /**
     * 修改分销员可提现金额
     *
     * @param commissionFrozen 分销金额
     * @param distributionId   分销员ID
     */
    @Update("UPDATE li_distribution set commission_frozen = (IFNULL(commission_frozen,0) - #{commissionFrozen}) " +
            ", rebate_total=(IFNULL(rebate_total,0) - #{commissionFrozen}) " +
            ", distribution_order_count=(IFNULL(distribution_order_count,0)-1) " +
            " WHERE id = #{distributionId}")
    void subRebate(Double commissionFrozen, String distributionId, Double distributionOrderPrice);

    /**
     * 添加分销冻结金额
     *
     * @param commissionFrozen 分销金额
     * @param distributionId   分销员ID
     */
    @Update("UPDATE li_distribution set commission_frozen = (IFNULL(commission_frozen,0)+#{commissionFrozen}) " +
            ", rebate_total=(IFNULL(rebate_total,0)+#{commissionFrozen}) " +
            ", distribution_order_price=(IFNULL(distribution_order_price,0)+#{distributionOrderPrice}) " +
            ", distribution_order_count=(IFNULL(distribution_order_count,0)+1) " +
            " WHERE id = #{distributionId}")
    void addRebate(Double commissionFrozen, String distributionId, Double distributionOrderPrice);

    /**
     * 分销团队情况
     *
     * @param distributionId 分销员ID
     * @param page           分页
     * @return
     */
    @Select("SELECT d.* FROM li_distribution_bind db INNER JOIN li_distribution d ON d.member_id = db.member_id WHERE db.distribution_id = #{distributionId}")
    Page<Distribution> distributionGroupPage(String distributionId, Page<Distribution> page);

    /**
     * 添加分销可提现金额
     *
     * @param rebate         分销金额
     * @param distributionId 分销员ID
     */
    @Update("UPDATE li_distribution SET commission_frozen = (IFNULL(commission_frozen,0) - #{rebate}) " +
            ",can_rebate=(IFNULL(can_rebate,0) + #{rebate}) " +
            " WHERE id = #{distributionId}")
    void addCanRebate(Double rebate, String distributionId);

    /**
     * 扣减分销员可提现金额
     *
     * @param rebate         佣金
     * @param distributionId 分销员
     */
    @Update("UPDATE li_distribution SET can_rebate=(IFNULL(can_rebate,0) - #{rebate}),cash_rebate=(IFNULL(cash_rebate,0) + #{rebate}) " +
            " WHERE id = #{distributionId}")
    void addCashRebate(Double rebate, String distributionId);

    /**
     * 扣减分销员提现佣金
     *
     * @param rebate
     * @param distributionId
     */
    @Update("UPDATE li_distribution SET cash_rebate=(IFNULL(cash_rebate,0) - #{rebate}) " +
            " WHERE id = #{distributionId}")
    void subCashRebate(Double rebate, String distributionId);

    /**
     * 分销团队情况
     *
     * @param distributionId 分销员ID
     * @return
     */
    @Select("""
        SELECT
            d.id AS distribution_id,
        
            -- 今日新增下线数量
            COUNT(DISTINCT CASE
                WHEN DATE(CONVERT_TZ(b.create_time, '+00:00', '+08:00')) = CURDATE()
                AND b.member_id IN (SELECT member_id FROM li_distribution)
                THEN b.member_id
                ELSE NULL
            END) AS today_people_num,
        
            -- 总计下线数量
            COUNT(DISTINCT CASE
                WHEN b.member_id IN (SELECT member_id FROM li_distribution)
                THEN b.member_id
                ELSE NULL
            END) AS total_people_num
        
        FROM
            li_distribution d
        LEFT JOIN
            li_distribution_bind b ON d.id = b.distribution_id
        WHERE
            d.id = #{distributionId};
        
         """)
    Map<String,Long> statistics(String distributionId);
}