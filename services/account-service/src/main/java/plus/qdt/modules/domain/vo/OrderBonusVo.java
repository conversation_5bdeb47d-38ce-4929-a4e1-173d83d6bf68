package plus.qdt.modules.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@Schema(description = "订单分红进度")
public class OrderBonusVo {

    @Schema(description = "子订单编号")
    private String orderSn;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "图片")
    private String image;

    @Schema(title = "订单金额")
    private BigDecimal money;

    @Schema(title = "分红总额")
    private BigDecimal bonusMoney;

    @Schema(title = "分红倍数")
    private Double splitRatio;

    @Schema(title = "进度")
    private Double progress;

    @Schema(title = "数量")
    private Integer num;

    @Schema(title = "规格")
    private String specs;
}
