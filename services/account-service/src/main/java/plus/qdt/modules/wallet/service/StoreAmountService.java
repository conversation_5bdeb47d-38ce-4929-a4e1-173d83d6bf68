package plus.qdt.modules.wallet.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.modules.domain.StoreAmount;
import plus.qdt.modules.domain.WithdrawAccountLog;
import plus.qdt.modules.domain.params.AmountLogParam;
import plus.qdt.modules.domain.params.WithdrawCashParam;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2.0
 */
public interface StoreAmountService extends IService<StoreAmount> {

    /**
     * 获取凭证key
     * @return {@link String}
     * <AUTHOR>
     */
    default String getKey() {
        return "account:verify:STORE:" + UserContext.getCurrentExistUser().getExtendId();
    }

    /**
     * 财通提现
     * @param param 提现参数
     * @return {@link boolean}
     * <AUTHOR>
     */
    boolean withdrawCash(WithdrawCashParam param);

    /**
     * 商家财通总额
     * @return {@link BigDecimal}
     * <AUTHOR>
     */
    BigDecimal sumMoney();

    /**
     * 获取账户信息
     * @param storeId 店铺ID
     * @return {@link StoreAmount}
     * <AUTHOR>
     */
    StoreAmount getAmount(String storeId);

    /**
     * 提现记录
     * @param param 查询参数
     * @return {@link Page} {@link WithdrawAccountLog}
     * <AUTHOR>
     */
    Page<WithdrawAccountLog> withdrawPage(AmountLogParam param);
}
