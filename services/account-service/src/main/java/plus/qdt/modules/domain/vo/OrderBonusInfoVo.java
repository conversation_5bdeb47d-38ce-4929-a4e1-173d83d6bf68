package plus.qdt.modules.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025-06-09
 */
@Data
@Schema(description = "订单分红信息")
public class OrderBonusInfoVo {

    @Schema(title = "分红总额")
    private BigDecimal bonusMoney;

    @Schema(title = "已分红")
    private BigDecimal signMoney;

    @Schema(title = "分红单数")
    private long total;

    @Schema(title = "未分红")
    private BigDecimal unBonusMoney;

}
