package plus.qdt.modules.wallet.serviceimpl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import plus.qdt.modules.domain.UserQiAmountLog;
import plus.qdt.modules.wallet.mapper.UserQiAmountLogMapper;
import plus.qdt.modules.wallet.service.UserQiAmountLogService;

/**
 * <AUTHOR>
 * @since 2.0
 */
@Service
public class UserQiAmountLogServiceImpl extends ServiceImpl<UserQiAmountLogMapper, UserQiAmountLog> implements UserQiAmountLogService {

}