package plus.qdt.modules.domain.params;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import plus.qdt.common.utils.StringUtils;

import java.util.List;

/**
 * 金通宝、借贷池、还贷池、分红池的列表查询参数
 */
@Data
public class LogQueryParam {

    @Schema(description = "查询类型")
    private String type;

    @Schema(description = "单号")
    private String orderNumber;

    @Schema(description = "开始日期")
    private String startDate;

    @Schema(description = "结算时间")
    private String endDate;

    /**
     * 类型
     * @return {@link List} {@link String}
     * <AUTHOR>
     */
    public List<String> getType() {
        if (StringUtils.isNotBlank(type)) {
            return StringUtils.split(type, ",");
        }
        return List.of();
    }
}
