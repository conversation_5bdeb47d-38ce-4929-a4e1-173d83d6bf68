package plus.qdt.modules.domain.params;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import plus.qdt.common.vo.PageVO;

/**
 * <AUTHOR>
 * @since 2.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AmountLogParam extends PageVO {

    @Schema(title = "开始时间")
    private String startTime;

    @Schema(title = "结束时间")
    private String endTime;

    @Schema(title = "类型", description = "红包、消费...")
    private Integer type;

}
