package plus.qdt.modules.payment.serviceimpl;

import plus.qdt.modules.payment.entity.dos.OutOrderDetailLog;
import plus.qdt.modules.payment.mapper.OutOrderDetailLogMapper;
import plus.qdt.modules.payment.service.OutOrderDetailLogService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 分账记录 业务实现
 *
 * <AUTHOR>
 * @since 2020-12-19 09:25
 */
@Slf4j
@Service
public class OutOrderDetailLogServiceImpl extends ServiceImpl<OutOrderDetailLogMapper, OutOrderDetailLog> implements OutOrderDetailLogService {


    @Override
    public List<OutOrderDetailLog> getByOutOrderNo(String outOrderNo) {
        LambdaQueryWrapper<OutOrderDetailLog> query = new LambdaQueryWrapper<OutOrderDetailLog>();
        query.eq(OutOrderDetailLog::getOutOrderNo, outOrderNo);
        query.orderBy(true,true,
                OutOrderDetailLog::getId);
        return list(query);
    }
}
