package plus.qdt.modules.kit;

import plus.qdt.common.utils.SnowFlake;
import plus.qdt.common.utils.SpringContextUtil;
import plus.qdt.modules.jinTongAccount.client.AccountConsumeClient;
import plus.qdt.modules.jinTongAccount.entity.AccountConsume;
import plus.qdt.modules.jinTongAccount.entity.vo.HostingAccountVo;
import plus.qdt.modules.jinTongAccount.enums.AccountTypeEnum;
import plus.qdt.modules.jinTongAccount.enums.CaiTongAccountLogEnum;
import plus.qdt.modules.jinTongAccount.enums.PayMethodEnum;
import plus.qdt.modules.payment.entity.dos.RefundLog;
import plus.qdt.modules.payment.entity.dto.RefundParam;
import plus.qdt.modules.payment.entity.enums.PaymentMethodEnum;
import plus.qdt.modules.payment.service.RefundLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 退款支持
 *
 * <AUTHOR>
 * @since 2020-12-19 09:25
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RefundSupport {

    private final RefundLogService refundLogService;

    private final AccountConsumeClient accountConsumeClient;

    /**
     * 售后退款
     *
     * @param refundParam 退款参数
     */
    @Transactional
    public RefundLog refund(RefundParam refundParam) {
        RefundLog refundLog = RefundLog.builder()
                .userId(refundParam.getUserId())
                .orderSn(refundParam.getOrderSn())
                .afterSaleNo(refundParam.getSn())
                .payPrice(refundParam.getPayPrice())
                .paymentMethod(refundParam.getPaymentMethod())
                .outTradeNo(refundParam.getOutTradeNo())
                .transactionId(refundParam.getTransactionId())
                .isRefund(false)
                .price(refundParam.getPrice())
                .refundReason(refundParam.getRefundReason())
                .build();

        // 校验售后单是否处理过
        refundLogService.check(refundParam.getSn());

        PaymentMethodEnum paymentMethodEnum = PaymentMethodEnum.paymentNameOf(refundParam.getPaymentMethod());

        // 生成售后请求号
        refundLog.setOutRefundNo(SnowFlake.getIdStr());

        // 获取订单支付类型
        HostingAccountVo hosting = refundParam.getHostingAccount();
        // 不是财通支付，说明是有现金的支付，直接退款
        if (!PayMethodEnum.COIN.equals(hosting.getPayMethod())) {
            if (paymentMethodEnum != null) {
                Payment payment = (Payment) SpringContextUtil.getBean(paymentMethodEnum.getPlugin());
                payment.refund(refundLog);
            }
        } else {
            this.refund(refundLog, refundParam.getTradeSn());
            refundLog.setIsRefund(true);
        }
        refundLogService.save(refundLog);
        return refundLog;
    }

    /**
     * 财通宝退款 从托管资金中扣减-反还财通宝给用户
     * @param refundLog 退款信息
     * @param tradeSn 交易订单号
     * <AUTHOR>
     */
    private void refund(RefundLog refundLog, String tradeSn) {
        // 财通宝支付，需从托管资金中退还财通
        accountConsumeClient.jinTongAccountConsume(new AccountConsume()
                .setAccountTypeEnum(AccountTypeEnum.CAI_TONG_ACCOUNT)
                .setSubType(CaiTongAccountLogEnum.CHARGEBACK.getSubType())
                .setNames(List.of(refundLog.getOrderSn()))
                .setMoney(BigDecimal.valueOf(refundLog.getPrice()))
                .setPayment(tradeSn, refundLog.getRefundReason())
        );
    }

    /**
     * 退款通知
     *
     * @param paymentMethodEnum 支付渠道
     */
    public void notify(PaymentMethodEnum paymentMethodEnum) {

        //获取支付插件
        Payment payment = (Payment) SpringContextUtil.getBean(paymentMethodEnum.getPlugin());
        payment.refundNotify();
    }
}
