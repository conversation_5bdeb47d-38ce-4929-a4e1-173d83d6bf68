<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="plus.qdt.modules.order.order.mapper.TpiOrderItemMapper">

    <!-- 批量插入商品明细 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO qdt_tpi_order_items (
            id, tpi_order_id, system_order_sn, third_party_order_id, supplier_type,
            goods_id, goods_name, sku_id, sku_name, supplier_goods_id, supplier_sku_id,
            goods_image, specs, category_path, unit_price, quantity, total_price,
            discount_amount, actual_amount, item_status, delivery_status,
            create_time, update_time, delete_flag
        ) VALUES
        <foreach collection="items" item="item" separator=",">
            (
                #{item.id}, #{item.tpiOrderId}, #{item.systemOrderSn}, #{item.thirdPartyOrderId}, #{item.supplierType},
                #{item.goodsId}, #{item.goodsName}, #{item.skuId}, #{item.skuName}, #{item.supplierGoodsId}, #{item.supplierSkuId},
                #{item.goodsImage}, #{item.specs}, #{item.categoryPath}, #{item.unitPrice}, #{item.quantity}, #{item.totalPrice},
                #{item.discountAmount}, #{item.actualAmount}, #{item.itemStatus}, #{item.deliveryStatus},
                NOW(), NOW(), 0
            )
        </foreach>
    </insert>


</mapper>
