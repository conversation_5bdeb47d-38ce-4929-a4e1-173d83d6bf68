package plus.qdt.modules.order.order.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import plus.qdt.modules.order.order.entity.dos.TpiOrderItem;

import java.util.List;

/**
 * 三方订单商品明细Mapper接口
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Mapper
public interface TpiOrderItemMapper extends BaseMapper<TpiOrderItem> {
    /**
     * 批量插入商品明细
     *
     * @param items 商品明细列表
     * @return 插入行数
     */
    int batchInsert(@Param("items") List<TpiOrderItem> items);
}
