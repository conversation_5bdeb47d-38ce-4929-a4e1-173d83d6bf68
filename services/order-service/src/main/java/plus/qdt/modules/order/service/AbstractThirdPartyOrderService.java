package plus.qdt.modules.order.service;

import plus.qdt.modules.goods.entity.enums.SupplierEnum;
import plus.qdt.modules.order.order.entity.dos.Order;
import plus.qdt.modules.order.order.entity.dos.OrderItem;
import plus.qdt.modules.order.order.entity.dto.ThirdPartyOrderResult;
import plus.qdt.modules.order.order.entity.dto.ThirdPartyCancelResult;

import java.util.List;

/**
 * 三方订单处理抽象基类
 * 使用模板方法模式定义三方订单创建的标准流程
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
public abstract class AbstractThirdPartyOrderService {

    /**
     * 三方订单创建模板方法
     * 注意：三方订单创建应该在订单支付完成后触发，基于 Order 和 OrderItem 数据
     *
     * @param order 系统订单
     * @param thirdPartyOrderItems 三方商品订单项
     * @return 三方订单创建结果
     */
    public final ThirdPartyOrderResult createThirdPartyOrder(Order order, List<OrderItem> thirdPartyOrderItems) {
        try {
            // 1. 前置验证
            if (!preValidate(order, thirdPartyOrderItems)) {
                return ThirdPartyOrderResult.failure(getSupplier(), "前置验证失败");
            }

            // 2. 获取认证信息
            String authToken = getAuthToken();
            if (authToken == null) {
                return ThirdPartyOrderResult.failure(getSupplier(), "获取认证信息失败");
            }

            // 3. 构建三方订单数据
            Object thirdPartyOrderData = buildThirdPartyOrderData(order, thirdPartyOrderItems);
            if (thirdPartyOrderData == null) {
                return ThirdPartyOrderResult.failure(getSupplier(), "构建三方订单数据失败");
            }

            // 4. 调用三方接口创建订单
            String thirdPartyOrderId = callThirdPartyCreateOrder(authToken, thirdPartyOrderData);
            if (thirdPartyOrderId == null) {
                return ThirdPartyOrderResult.failure(getSupplier(), "调用三方创建订单接口失败");
            }

            // 5. 后置处理
            postProcess(order, thirdPartyOrderId, thirdPartyOrderItems);

            return ThirdPartyOrderResult.success(getSupplier(), thirdPartyOrderId, thirdPartyOrderItems.size());

        } catch (Exception e) {
            return ThirdPartyOrderResult.failure(getSupplier(), "创建三方订单异常: " + e.getMessage());
        }
    }

    /**
     * 三方订单取消模板方法
     *
     * @param order 系统订单
     * @param thirdPartyOrderId 三方订单ID
     * @return 三方订单取消结果
     * <AUTHOR>
     */
    public final ThirdPartyCancelResult cancelThirdPartyOrder(Order order, String thirdPartyOrderId) {
        try {
            // 1. 前置验证
            if (!preCancelValidate(order, thirdPartyOrderId)) {
                return ThirdPartyCancelResult.failure(getSupplier(), order.getSn(), "取消前置验证失败");
            }

            // 2. 获取认证信息
            String authToken = getAuthToken();
            if (authToken == null) {
                return ThirdPartyCancelResult.failure(getSupplier(), order.getSn(), "获取认证信息失败");
            }

            // 3. 构建取消订单数据
            Object cancelOrderData = buildCancelOrderData(order, thirdPartyOrderId);
            if (cancelOrderData == null) {
                return ThirdPartyCancelResult.failure(getSupplier(), order.getSn(), "构建取消订单数据失败");
            }

            // 4. 调用三方接口取消订单
            String cancelResult = callThirdPartyCancelOrder(authToken, cancelOrderData);
            if (cancelResult == null) {
                return ThirdPartyCancelResult.failure(getSupplier(), order.getSn(), "调用三方取消订单接口失败");
            }

            // 5. 后置处理
            postCancelProcess(order, thirdPartyOrderId, cancelResult);

            return ThirdPartyCancelResult.success(getSupplier(), order.getSn(), thirdPartyOrderId, cancelResult);

        } catch (Exception e) {
            return ThirdPartyCancelResult.failure(getSupplier(), order.getSn(), "取消三方订单异常: " + e.getMessage());
        }
    }

    // ==================== 抽象方法（子类必须实现） ====================

    /**
     * 获取供应商类型
     * <AUTHOR>
     */
    protected abstract SupplierEnum getSupplier();

    /**
     * 前置验证
     * 验证订单和订单项数据的有效性
     * @param order 系统订单
     * @param thirdPartyOrderItems 三方订单项
     * @return 验证结果
     * <AUTHOR>
     */
    protected abstract boolean preValidate(Order order, List<OrderItem> thirdPartyOrderItems);

    /**
     * 获取认证Token
     * 获取调用三方接口所需的认证令牌
     * @return 认证令牌
     * <AUTHOR>
     */
    protected abstract String getAuthToken();

    /**
     * 构建三方订单数据
     * 将系统订单数据转换为三方接口所需的格式
     * @param order 系统订单
     * @param thirdPartyOrderItems 三方订单项
     * @return 三方订单数据
     * <AUTHOR>
     */
    protected abstract Object buildThirdPartyOrderData(Order order, List<OrderItem> thirdPartyOrderItems);

    /**
     * 调用三方创建订单接口
     * 实际调用三方系统的订单创建接口
     * @param authToken 认证令牌
     * @param orderData 订单数据
     * @return 三方订单ID
     * <AUTHOR>
     */
    protected abstract String callThirdPartyCreateOrder(String authToken, Object orderData);

    /**
     * 后置处理
     * 订单创建成功后的处理逻辑，如保存三方订单记录等
     * @param order 系统订单
     * @param thirdPartyOrderId 三方订单ID
     * @param orderItems 订单项
     * <AUTHOR>
     */
    protected void postProcess(Order order, String thirdPartyOrderId, List<OrderItem> orderItems) {
        // 默认空实现，子类可以重写
    }

    // ==================== 取消订单抽象方法（子类必须实现） ====================

    /**
     * 取消前置验证
     * 验证订单和三方订单ID的有效性
     * @param order 系统订单
     * @param thirdPartyOrderId 三方订单ID
     * @return 验证结果
     * <AUTHOR>
     */
    protected abstract boolean preCancelValidate(Order order, String thirdPartyOrderId);

    /**
     * 构建取消订单数据
     * 将系统订单数据转换为三方取消接口所需的格式
     * @param order 系统订单
     * @param thirdPartyOrderId 三方订单ID
     * @return 取消订单数据
     * <AUTHOR>
     */
    protected abstract Object buildCancelOrderData(Order order, String thirdPartyOrderId);

    /**
     * 调用三方取消订单接口
     * 实际调用三方系统的订单取消接口
     * @param authToken 认证令牌
     * @param cancelOrderData 取消订单数据
     * @return 取消结果状态
     * <AUTHOR>
     */
    protected abstract String callThirdPartyCancelOrder(String authToken, Object cancelOrderData);

    /**
     * 取消后置处理
     * 订单取消成功后的处理逻辑，如更新订单记录状态等
     * @param order 系统订单
     * @param thirdPartyOrderId 三方订单ID
     * @param cancelResult 取消结果
     * <AUTHOR>
     */
    protected abstract void postCancelProcess(Order order, String thirdPartyOrderId, String cancelResult);
}
