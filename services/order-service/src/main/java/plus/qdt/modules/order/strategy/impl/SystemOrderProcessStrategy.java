package plus.qdt.modules.order.strategy.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import plus.qdt.modules.goods.entity.enums.SupplierEnum;
import plus.qdt.modules.order.cart.entity.dto.TradeDTO;
import plus.qdt.modules.order.cart.entity.vo.CartVO;
import plus.qdt.modules.order.cart.entity.vo.CartSkuVO;
import plus.qdt.modules.order.order.entity.dos.Trade;
import plus.qdt.modules.order.order.service.TradeService;
import plus.qdt.modules.order.strategy.OrderProcessStrategy;

/**
 * 系统订单处理策略
 * 处理系统自有商品的订单创建逻辑
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@Component
public class SystemOrderProcessStrategy implements OrderProcessStrategy {

    @Autowired
    private TradeService tradeService;

    @Override
    public Trade processOrder(TradeDTO tradeDTO) {
        log.info("使用系统订单处理策略创建订单，交易编号: {}", tradeDTO.getSn());
        
        // 过滤出系统商品
        TradeDTO systemTradeDTO = filterSystemGoods(tradeDTO);
        
        if (systemTradeDTO.getCartList().isEmpty()) {
            log.warn("没有系统商品需要处理，交易编号: {}", tradeDTO.getSn());
            return null;
        }
        
        // 使用原有的系统订单创建逻辑
        return tradeService.createTrade(systemTradeDTO);
    }

    @Override
    public boolean supports(TradeDTO tradeDTO) {
        // 检查是否包含系统商品
        return tradeDTO.getCartList().stream()
                .anyMatch(this::isSystemGoods);
    }

    @Override
    public int getPriority() {
        return 100; // 系统订单优先级较低，让三方订单先处理
    }

    /**
     * 过滤出系统商品
     *
     * @param originalTradeDTO 原始交易信息
     * @return 只包含系统商品的交易信息
     */
    private TradeDTO filterSystemGoods(TradeDTO originalTradeDTO) {
        TradeDTO systemTradeDTO = new TradeDTO();
        
        // 复制基本信息
        copyBasicInfo(originalTradeDTO, systemTradeDTO);
        
        // 过滤系统商品
        systemTradeDTO.getCartList().addAll(
            originalTradeDTO.getCartList().stream()
                .filter(this::isSystemGoods)
                .toList()
        );
        
        return systemTradeDTO;
    }

    /**
     * 判断是否为系统商品
     *
     * @param cartVO 购物车项
     * @return 是否为系统商品
     */
    private boolean isSystemGoods(CartVO cartVO) {
        // 检查购物车中是否有系统商品
        return cartVO.getCheckedSkuList().stream()
                .anyMatch(this::isSystemGoodsSku);
    }

    /**
     * 判断单个SKU是否为系统商品
     *
     * @param cartSkuVO 购物车SKU项
     * @return 是否为系统商品
     */
    private boolean isSystemGoodsSku(CartSkuVO cartSkuVO) {
        String supplierEnum = cartSkuVO.getGoodsSku().getSupplierEnum();

        // 系统商品的判断条件：
        // 1. supplierEnum为空或为CUSTOM
        // 2. 三方skuId为null
        return supplierEnum == null ||
               SupplierEnum.CUSTOM.name().equals(supplierEnum) ||
               null == cartSkuVO.getGoodsSku().getSupplierSkuId();
    }

    /**
     * 复制基本信息
     *
     * @param source 源交易信息
     * @param target 目标交易信息
     */
    private void copyBasicInfo(TradeDTO source, TradeDTO target) {
        target.setSn(source.getSn());
        target.setMemberId(source.getMemberId());
        target.setMemberName(source.getMemberName());
        target.setUserAddress(source.getUserAddress());
        target.setClientType(source.getClientType());
        target.setStoreRemark(source.getStoreRemark());
        target.setParentOrderSn(source.getParentOrderSn());
        target.setAreaCode(source.getAreaCode());
        target.setCartSceneEnum(source.getCartSceneEnum());
        target.setPromotionType(source.getPromotionType());
        target.setNeedReceipt(source.getNeedReceipt());
        target.setReceiptVO(source.getReceiptVO());
        target.setPlatformCoupon(source.getPlatformCoupon());
        target.setStoreCoupons(source.getStoreCoupons());
        target.setPriceDetailDTO(source.getPriceDetailDTO());
    }
}
