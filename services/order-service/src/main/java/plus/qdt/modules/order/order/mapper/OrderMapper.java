package plus.qdt.modules.order.order.mapper;

import plus.qdt.modules.domain.dto.PayOrderTopDto;
import plus.qdt.modules.order.order.entity.dos.Order;
import plus.qdt.modules.order.order.entity.dto.OrderExportDTO;
import plus.qdt.modules.order.order.entity.vo.OrderSimpleVO;
import plus.qdt.modules.payment.entity.dos.PaymentLog;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 订单数据处理层
 *
 * <AUTHOR>
 * @since 2020/11/17 7:35 下午
 */
public interface OrderMapper extends BaseMapper<Order> {

    /**
     * 修改订单状态
     *
     * @param status  状态
     * @param orderSn 订单编号
     */
    @Update({"update li_order set order_status = #{status} where sn = #{orderSn}"})
    void updateStatus(String status, String orderSn);

    /**
     * 查询订单简短信息分页
     *
     * @param page         分页
     * @param queryWrapper 查询条件
     * @return 简短订单分页
     */
    @Select("select o.sn,o.trade_sn,o.flow_price,o.create_time,o.order_status,o.pay_status,o.payment_method,o.payment_time," +
            "o.nickname,o.store_name as store_name,o.store_id as store_id,o.client_type,o.order_type,o.deliver_status," +
            "o.order_promotion_type,o.seller_remark " +
            ",GROUP_CONCAT(oi.goods_id) as group_goods_id," +
            " GROUP_CONCAT(oi.sku_id) as group_sku_id," +
            " GROUP_CONCAT(oi.num) as group_num" +
            ",GROUP_CONCAT(oi.image) as group_images" +
            ",GROUP_CONCAT(oi.goods_name) as group_name " +
            ",GROUP_CONCAT(oi.specs) as group_specs " +
            ",GROUP_CONCAT(oi.after_sale_status) as group_after_sale_status" +
            ",GROUP_CONCAT(oi.complain_status) as group_complain_status" +
            ",GROUP_CONCAT(oi.comment_status) as group_comment_status" +
            ",GROUP_CONCAT(oi.sn) as group_order_items_sn " +
            ",GROUP_CONCAT(oi.goods_price) as group_goods_price " +
            " FROM li_order o INNER JOIN li_order_item AS oi on o.sn = oi.order_sn ${ew.customSqlSegment} ")
    Page<OrderSimpleVO> queryByParams(Page<OrderSimpleVO> page, @Param(Constants.WRAPPER) Wrapper<OrderSimpleVO> queryWrapper);

    /**
     * 查询导出订单DTO列表
     *
     * @param queryWrapper 查询条件
     * @return 导出订单DTO列表
     */
    @Select("SELECT o.sn AS order_sn," +
            "oi.sn AS order_item_sn," +
            "oi.goods_name AS goods_name," +
            "oi.num AS num," +
            "oi.goods_id AS goods_id," +
            "oi.unit_price AS unit_price," +
            "oi.flow_price AS flow_price," +
            "oi.price_detail AS price_detail," +
            "o.payment_method AS payment_method," +
            "o.consignee_name AS consignee_name," +
            "o.consignee_mobile AS consignee_mobile," +
            "o.consignee_address_path AS consignee_address_path," +
            "o.consignee_detail AS consignee_detail," +
            "o.remark AS remark," +
            "o.create_time AS create_time," +
            "o.payment_time AS payment_time," +
            "o.client_type AS client_type," +
            "o.order_status AS order_status," +
            "o.order_type AS order_type," +
            "oi.after_sale_status AS after_sale_status," +
            "o.logistics_time AS logistics_time," +
            "o.complete_time AS complete_time," +
            "o.cancel_reason AS cancel_reason," +
            "o.store_name AS store_name " +
            " FROM li_order o LEFT JOIN li_order_item oi ON oi.order_sn = o.sn ${ew.customSqlSegment}")
    List<OrderExportDTO> queryExportOrder(@Param(Constants.WRAPPER) Wrapper<OrderSimpleVO> queryWrapper);

    /**
     * 查询订单支付记录
     *
     * @param page         分页
     * @param queryWrapper 查询条件
     * @return 订单支付记录分页
     */
    @Select("select o.* from li_order o ${ew.customSqlSegment} ")
    Page<PaymentLog> queryPaymentLogs(Page<PaymentLog> page, @Param(Constants.WRAPPER) Wrapper<PaymentLog> queryWrapper);

    /**
     * 查询订单信息
     *
     * @param queryWrapper 查询条件
     * @return 简短订单分页
     */
    @Select("select o.* " +
            " FROM li_order o INNER JOIN li_order_item AS oi on o.sn = oi.order_sn ${ew.customSqlSegment} ")
    List<Order> queryListByParams(@Param(Constants.WRAPPER) Wrapper<Order> queryWrapper);

    /**
     * 查询订单信息
     *
     * @param orderPromotionType 订单促销类型
     * @param payStatus          支付状态
     * @param parentOrderSn      父订单编号
     * @param promotionId        促销活动ID
     * @param skuId              skuId
     * @return 简短订单分页
     */
    @Select("select o.* " +
            " FROM li_order o INNER JOIN li_order_item AS oi on o.sn = oi.order_sn where o.order_promotion_type = #{orderPromotionType} and o" +
            ".pay_status = #{payStatus} and o.parent_order_sn = #{parentOrderSn} and o.promotion_id = #{promotionId} and oi.sku_id = #{skuId} ")
    List<Order> queryListByPromotionId(String orderPromotionType, String payStatus, String parentOrderSn, String promotionId, String skuId);

    /**
     * 获取自营商品已支付商品订单
     * @param operatedIds 自营店铺ID
     * @return {@link List} {@link Order}
     * <AUTHOR>
     */
    List<PayOrderTopDto> orderTop(@Param("operatedIds") List<String> operatedIds);

    /**
     * 统计用户订单各状态数量
     * 使用单一SQL查询提高性能，统计待付款、待发货、待收货、待评价、退换货订单数量
     *
     * @param userId 用户ID
     * @return 订单状态数量统计Map，key为状态类型，value为数量
     * <AUTHOR>
     */
    @Select("""
            SELECT
                SUM(CASE WHEN o.order_status = 'UNPAID' THEN 1 ELSE 0 END) as unpaidCount,
                SUM(CASE WHEN o.order_status = 'UNDELIVERED' THEN 1 ELSE 0 END) as undeliveredCount,
                SUM(CASE WHEN o.order_status = 'DELIVERED' THEN 1 ELSE 0 END) as deliveredCount,
                SUM(CASE WHEN o.order_status = 'COMPLETED' AND EXISTS(
                    SELECT 1 FROM li_order_item oi
                    WHERE oi.order_sn = o.sn
                    AND oi.comment_status IN ('NEW', 'UNFINISHED')
                ) THEN 1 ELSE 0 END) as unfinishedCommentCount,
                SUM(CASE WHEN EXISTS(
                    SELECT 1 FROM li_order_item oi
                    WHERE oi.order_sn = o.sn
                    AND oi.after_sale_status IN ('ALREADY_APPLIED', 'PART_AFTER_SALE')
                ) THEN 1 ELSE 0 END) as afterSaleCount
            FROM li_order o
            WHERE o.member_id = #{userId}
            AND o.delete_flag = 0
            """)
    java.util.Map<String, Long> countOrderStatusByUserId(@Param("userId") String userId);
}