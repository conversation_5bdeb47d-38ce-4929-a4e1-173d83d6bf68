package plus.qdt.modules.order.strategy;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import plus.qdt.modules.order.cart.entity.dto.TradeDTO;
import plus.qdt.modules.order.cart.entity.vo.CartVO;
import plus.qdt.modules.order.cart.entity.vo.CartSkuVO;
import plus.qdt.modules.order.order.entity.dos.Trade;
import plus.qdt.modules.order.factory.ThirdPartyOrderServiceFactory;
import plus.qdt.modules.order.service.AbstractThirdPartyOrderService;
import plus.qdt.modules.goods.entity.enums.SupplierEnum;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单处理策略管理器
 * 使用责任链模式管理多种订单处理策略
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@Component
public class OrderProcessStrategyManager {

    @Autowired
    private List<OrderProcessStrategy> strategies;

    @Autowired
    private ThirdPartyOrderServiceFactory serviceFactory;

    /**
     * 处理三方商品订单（在系统订单创建后调用）
     *
     * @param tradeDTO 交易信息
     * @param systemTrade 已创建的系统订单
     */
    public void processThirdPartyOrders(TradeDTO tradeDTO, Trade systemTrade) {
        try {
            log.info("开始处理三方商品订单，系统订单号: {}", systemTrade.getSn());

            // 按供应商分组处理三方商品
            Map<SupplierEnum, List<CartVO>> supplierGroups = groupThirdPartyGoodsBySupplier(tradeDTO);

            if (supplierGroups.isEmpty()) {
                log.info("当前订单无三方商品，系统订单号: {}", systemTrade.getSn());
                return;
            }

            // 处理每个供应商的订单
            for (Map.Entry<SupplierEnum, List<CartVO>> entry : supplierGroups.entrySet()) {
                SupplierEnum supplier = entry.getKey();
                List<CartVO> cartItems = entry.getValue();

                processSupplierOrder(tradeDTO, systemTrade, supplier, cartItems);
            }

        } catch (Exception e) {
            log.error("处理三方商品时发生异常，系统订单号: {}, 异常: {}", systemTrade.getSn(), e.getMessage(), e);
            // 注意：这里不抛出异常，因为系统订单已经创建成功
        }
    }

    /**
     * 按供应商分组三方商品
     *
     * @param tradeDTO 交易信息
     * @return 按供应商分组的商品
     */
    private Map<SupplierEnum, List<CartVO>> groupThirdPartyGoodsBySupplier(TradeDTO tradeDTO) {
        Map<SupplierEnum, List<CartVO>> supplierGroups = new HashMap<>();

        for (CartVO cartVO : tradeDTO.getCartList()) {
            // 按供应商分组SKU
            Map<SupplierEnum, List<CartSkuVO>> skuGroups = cartVO.getCheckedSkuList().stream()
                    .filter(this::isThirdPartyGoods)
                    .collect(Collectors.groupingBy(this::getSupplierEnum));

            // 为每个供应商创建CartVO
            for (Map.Entry<SupplierEnum, List<CartSkuVO>> skuEntry : skuGroups.entrySet()) {
                SupplierEnum supplier = skuEntry.getKey();
                List<CartSkuVO> skuList = skuEntry.getValue();

                CartVO supplierCartVO = new CartVO();
                supplierCartVO.setStoreId(cartVO.getStoreId());
                supplierCartVO.setStoreName(cartVO.getStoreName());
                supplierCartVO.setSkuList(skuList);

                supplierGroups.computeIfAbsent(supplier, k -> new ArrayList<>()).add(supplierCartVO);
            }
        }

        return supplierGroups;
    }

    /**
     * 判断是否为三方商品
     *
     * @param cartSkuVO 购物车SKU项
     * @return 是否为三方商品
     */
    private boolean isThirdPartyGoods(CartSkuVO cartSkuVO) {
        String supplierEnum = cartSkuVO.getGoodsSku().getSupplierEnum();
        String supplierSkuId = cartSkuVO.getGoodsSku().getSupplierSkuId();
        return supplierEnum != null &&
               !SupplierEnum.CUSTOM.name().equals(supplierEnum) &&
               supplierSkuId != null && !supplierSkuId.trim().isEmpty();
    }

    /**
     * 获取供应商枚举
     *
     * @param cartSkuVO 购物车SKU项
     * @return 供应商枚举
     */
    private SupplierEnum getSupplierEnum(CartSkuVO cartSkuVO) {
        String supplierEnumStr = cartSkuVO.getGoodsSku().getSupplierEnum();
        try {
            return SupplierEnum.valueOf(supplierEnumStr);
        } catch (Exception e) {
            log.warn("无法解析供应商类型: {}", supplierEnumStr);
            return SupplierEnum.CUSTOM;
        }
    }

    /**
     * 处理单个供应商的订单
     *
     * @param tradeDTO 交易信息
     * @param systemTrade 系统订单
     * @param supplier 供应商
     * @param cartItems 购物车项
     */
    private void processSupplierOrder(TradeDTO tradeDTO, Trade systemTrade, SupplierEnum supplier, List<CartVO> cartItems) {
        try {
            log.info("开始处理{}订单，系统订单号: {}, 商品数量: {}",
                    supplier.getDescription(), systemTrade.getSn(), cartItems.size());

            // 获取对应的三方订单服务
            AbstractThirdPartyOrderService orderService = serviceFactory.getService(supplier);
            if (orderService == null) {
                log.error("未找到{}的订单服务实现，系统订单号: {}", supplier.getDescription(), systemTrade.getSn());
                return;
            }

            // 注意：三方订单创建已移至订单支付完成后处理
            // 此处不再创建三方订单，只记录日志和做预处理
            log.info("检测到{}商品，系统订单号: {}，商品数量: {}。三方订单将在支付完成后创建。",
                    supplier.getDescription(), systemTrade.getSn(), cartItems.size());

            // 可以在这里做一些预处理，比如验证商品信息等
            // 但不应该创建实际的三方订单

        } catch (Exception e) {
            log.error("创建{}三方订单时发生异常，系统订单号: {}, 异常: {}",
                    supplier.getDescription(), systemTrade.getSn(), e.getMessage(), e);
        }
    }


}
