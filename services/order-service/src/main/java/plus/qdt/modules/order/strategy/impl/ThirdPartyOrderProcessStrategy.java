package plus.qdt.modules.order.strategy.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import plus.qdt.modules.goods.entity.enums.SupplierEnum;
import plus.qdt.modules.order.cart.entity.dto.TradeDTO;
import plus.qdt.modules.order.cart.entity.vo.CartSkuVO;
import plus.qdt.modules.order.cart.entity.vo.CartVO;
import plus.qdt.modules.order.factory.ThirdPartyOrderServiceFactory;
import plus.qdt.modules.order.order.entity.dos.Trade;
import plus.qdt.modules.order.order.entity.dto.ThirdPartyOrderResult;
import plus.qdt.modules.order.service.AbstractThirdPartyOrderService;
import plus.qdt.modules.order.strategy.OrderProcessStrategy;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 三方订单处理策略
 * 处理三方供应商商品的订单创建逻辑
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@Component
public class ThirdPartyOrderProcessStrategy implements OrderProcessStrategy {

    @Autowired
    private ThirdPartyOrderServiceFactory serviceFactory;

    @Override
    public Trade processOrder(TradeDTO tradeDTO) {
        log.info("使用三方订单处理策略创建订单，交易编号: {}", tradeDTO.getSn());
        
        // 按供应商分组三方商品
        Map<SupplierEnum, List<CartVO>> supplierGroups = groupBySupplier(tradeDTO);
        
        if (supplierGroups.isEmpty()) {
            log.warn("没有三方商品需要处理，交易编号: {}", tradeDTO.getSn());
            return null;
        }
        
        // 处理每个供应商的订单
        boolean allSuccess = true;
        StringBuilder errorMessages = new StringBuilder();
        
        for (Map.Entry<SupplierEnum, List<CartVO>> entry : supplierGroups.entrySet()) {
            SupplierEnum supplier = entry.getKey();
            List<CartVO> cartItems = entry.getValue();
            
            try {
                boolean success = processSupplierOrder(tradeDTO, supplier, cartItems);
                if (!success) {
                    allSuccess = false;
                    errorMessages.append(supplier.getDescription()).append("订单创建失败; ");
                }
            } catch (Exception e) {
                allSuccess = false;
                errorMessages.append(supplier.getDescription()).append("订单创建异常: ").append(e.getMessage()).append("; ");
                log.error("处理{}订单时发生异常: {}", supplier.getDescription(), e.getMessage(), e);
            }
        }
        
        if (!allSuccess) {
            throw new RuntimeException("三方订单创建失败: " + errorMessages.toString());
        }
        
        // 三方订单创建成功，返回一个标识性的Trade对象
        // 注意：这里不创建实际的Trade记录，因为三方订单有自己的订单号
        Trade trade = new Trade();
        trade.setSn(tradeDTO.getSn());
        trade.setMemberId(tradeDTO.getMemberId());
        trade.setMemberName(tradeDTO.getMemberName());
        
        log.info("三方订单处理完成，交易编号: {}", tradeDTO.getSn());
        return trade;
    }

    @Override
    public boolean supports(TradeDTO tradeDTO) {
        // 检查是否包含三方商品
        return tradeDTO.getCartList().stream()
                .anyMatch(this::isThirdPartyGoods);
    }

    @Override
    public int getPriority() {
        return 50; // 三方订单优先级较高，先处理
    }

    /**
     * 按供应商分组三方商品
     *
     * @param tradeDTO 交易信息
     * @return 按供应商分组的商品
     */
    private Map<SupplierEnum, List<CartVO>> groupBySupplier(TradeDTO tradeDTO) {
        Map<SupplierEnum, List<CartVO>> supplierGroups = new HashMap<>();

        for (CartVO cartVO : tradeDTO.getCartList()) {
            if (isThirdPartyGoods(cartVO)) {
                SupplierEnum supplier = getSupplierEnum(cartVO);
                supplierGroups.computeIfAbsent(supplier, k -> new ArrayList<>()).add(cartVO);
            }
        }

        return supplierGroups;
    }

    /**
     * 处理单个供应商的订单
     *
     * @param tradeDTO 交易信息
     * @param supplier 供应商
     * @param cartItems 购物车项
     * @return 是否成功
     */
    private boolean processSupplierOrder(TradeDTO tradeDTO, SupplierEnum supplier, List<CartVO> cartItems) {
        log.info("开始处理{}订单，商品数量: {}", supplier.getDescription(), cartItems.size());
        
        // 获取对应的三方订单服务
        AbstractThirdPartyOrderService orderService = serviceFactory.getService(supplier);
        if (orderService == null) {
            return false;
        }
        
        // 注意：三方订单创建已移至订单支付完成后处理
        // 此处不再创建三方订单，只记录日志和做预处理
        log.info("检测到{}商品，商品数量: {}。三方订单将在支付完成后创建。",
                supplier.getDescription(), cartItems.size());

        // 可以在这里做一些预处理，比如验证商品信息等
        // 但不应该创建实际的三方订单

        return true; // 预处理成功
    }

    /**
     * 判断是否为三方商品
     *
     * @param cartVO 购物车项
     * @return 是否为三方商品
     */
    private boolean isThirdPartyGoods(CartVO cartVO) {
        // 检查购物车中是否有三方商品
        return cartVO.getCheckedSkuList().stream()
                .anyMatch(this::isThirdPartyGoodsSku);
    }

    /**
     * 判断单个SKU是否为三方商品
     *
     * @param cartSkuVO 购物车SKU项
     * @return 是否为三方商品
     */
    private boolean isThirdPartyGoodsSku(CartSkuVO cartSkuVO) {
        String supplierEnum = cartSkuVO.getGoodsSku().getSupplierEnum();
        String supplierSkuId = cartSkuVO.getGoodsSku().getSupplierSkuId();

        // 三方商品的判断条件：
        // 1. supplierEnum不为空且不为CUSTOM
        // 2. 且supplierSkuId不为空（有三方SKU ID）
        return supplierEnum != null &&
               !SupplierEnum.CUSTOM.name().equals(supplierEnum) &&
               supplierSkuId != null && !supplierSkuId.trim().isEmpty();
    }

    /**
     * 获取供应商枚举
     *
     * @param cartVO 购物车项
     * @return 供应商枚举
     */
    private SupplierEnum getSupplierEnum(CartVO cartVO) {
        // 从购物车的第一个三方商品SKU中获取供应商类型
        return cartVO.getCheckedSkuList().stream()
                .filter(this::isThirdPartyGoodsSku)
                .findFirst()
                .map(this::getSupplierEnumFromSku)
                .orElse(SupplierEnum.CUSTOM);
    }

    /**
     * 从SKU获取供应商枚举
     *
     * @param cartSkuVO 购物车SKU项
     * @return 供应商枚举
     */
    private SupplierEnum getSupplierEnumFromSku(CartSkuVO cartSkuVO) {
        String supplierEnumStr = cartSkuVO.getGoodsSku().getSupplierEnum();
        try {
            return SupplierEnum.valueOf(supplierEnumStr);
        } catch (Exception e) {
            log.warn("无法解析供应商类型: {}", supplierEnumStr);
            return SupplierEnum.CUSTOM;
        }
    }

    /**
     * 保存三方订单信息到本地数据库
     *
     * @param tradeDTO 交易信息
     * @param result 三方订单结果
     */
    private void saveThirdPartyOrderInfo(TradeDTO tradeDTO, ThirdPartyOrderResult result) {
        // TODO: 实现保存三方订单信息的逻辑
        // 可以创建一个ThirdPartyOrder实体来保存：
        // - 本地交易编号
        // - 三方订单ID
        // - 供应商类型
        // - 订单状态
        // - 创建时间等
        
        log.debug("保存三方订单信息: 交易编号={}, 三方订单ID={}, 供应商={}", 
                 tradeDTO.getSn(), result.getThirdPartyOrderId(), result.getSupplier().getDescription());
    }
}
