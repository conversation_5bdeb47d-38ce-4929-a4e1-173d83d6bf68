package plus.qdt.modules.order.order.serviceimpl;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import plus.qdt.common.tpi.yzh.TpiHttpUtils;
import plus.qdt.common.tpi.yzh.config.YzhTpiProperties;
import plus.qdt.common.utils.HttpUtils;
import plus.qdt.modules.goods.entity.enums.SupplierEnum;

import plus.qdt.modules.order.order.entity.dos.Order;
import plus.qdt.modules.order.order.entity.dos.OrderItem;
import plus.qdt.modules.order.order.entity.dos.TpiOrderRecord;
import plus.qdt.modules.order.order.mapper.TpiOrderRecordMapper;
import plus.qdt.modules.order.order.utils.YzhOrderItemFilter;
import plus.qdt.modules.order.feign.ThirdPartyAddressClient;
import plus.qdt.modules.order.service.AbstractThirdPartyOrderService;
import plus.qdt.modules.goods.client.GoodsClient;
import plus.qdt.modules.goods.entity.dos.Goods;
import plus.qdt.modules.goods.entity.dto.GoodsSearchParams;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.order.order.entity.dto.YzhCancelOrderRequestParam;
import plus.qdt.modules.order.order.entity.dto.YzhCancelOrderResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 云中鹤三方订单服务实现
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@Service
public class YzhThirdPartyOrderServiceImpl extends AbstractThirdPartyOrderService {

    @Autowired
    private TpiHttpUtils tpiHttpUtils;

    @Autowired
    private YzhTpiProperties tpiProperties;

    @Autowired
    private ThirdPartyAddressClient thirdPartyAddressClient;

    @Autowired
    private TpiOrderRecordMapper tpiOrderRecordMapper;

    @Autowired
    private GoodsClient goodsClient;

    /**
     * 获取供应商类型
     *
     * @return 供应商枚举
     * <AUTHOR>
     */
    @Override
    protected SupplierEnum getSupplier() {
        return SupplierEnum.YZH;
    }

    /**
     * 前置验证，检查配置、商品、地址等信息
     *
     * @param order 系统订单
     * @param thirdPartyOrderItems 三方订单项
     * @return 验证结果
     * <AUTHOR>
     */
    @Override
    protected boolean preValidate(Order order, List<OrderItem> thirdPartyOrderItems) {
        // 1. 检查配置
        if (tpiProperties.getAppKey() == null || tpiProperties.getAppSecret() == null) {
            log.error("云中鹤配置不完整：appKey或appSecret为空");
            return false;
        }

        // 2. 检查是否有云中鹤商品
        if (!YzhOrderItemFilter.hasYzhOrderItems(thirdPartyOrderItems)) {
            log.warn("没有找到云中鹤商品");
            return false;
        }

        // 3. 检查收货地址
        if (order.getConsigneeName() == null || order.getConsigneeAddressIdPath() == null) {
            log.error("收货地址不能为空");
            return false;
        }

        // 4. 检查商品数量
        List<OrderItem> yzhOrderItems = filterYzhOrderItems(thirdPartyOrderItems);
        for (OrderItem orderItem : yzhOrderItems) {
            if (orderItem.getNum() <= 0) {
                log.error("商品数量必须大于0，SKU: {}", orderItem.getSkuId());
                return false;
            }
        }

        return true;
    }

    /**
     * 获取云中鹤认证Token
     *
     * @return 认证Token
     * <AUTHOR>
     */
    @Override
    protected String getAuthToken() {
        try {
            return tpiHttpUtils.getToken();
        } catch (Exception e) {
            log.error("获取云中鹤Token失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 构建云中鹤订单数据
     *
     * @param order 系统订单
     * @param thirdPartyOrderItems 三方订单项
     * @return 订单数据
     * <AUTHOR>
     */
    @Override
    protected Object buildThirdPartyOrderData(Order order, List<OrderItem> thirdPartyOrderItems) {
        try {
            // 过滤出云中鹤商品
            List<OrderItem> yzhOrderItems = filterYzhOrderItems(thirdPartyOrderItems);

            Map<String, Object> orderData = new HashMap<>();

            // 基本订单信息
            orderData.put("tparOrderCode", order.getSn());
            orderData.put("orderAmount", calculateOrderAmountFromItems(yzhOrderItems));
            orderData.put("orderFreightAmount", order.getFreightPrice() != null ? order.getFreightPrice() : 0.0);
            orderData.put("submitState", 1001);

            // 收货人信息
            orderData.put("receiveName", order.getConsigneeName());
            orderData.put("receiveMobile", order.getConsigneeMobile());

            // 地址信息 - 只有转换结果不为null时才添加到请求数据中
            Integer provinceId = parseProvinceIdFromOrder(order);
            if (provinceId != null) {
                orderData.put("provinceId", provinceId);
            }

            Integer cityId = parseCityIdFromOrder(order);
            if (cityId != null) {
                orderData.put("cityId", cityId);
            }

            Integer countyId = parseCountyIdFromOrder(order);
            if (countyId != null) {
                orderData.put("countyId", countyId);
            }

            Integer townId = parseTownIdFromOrder(order);
            if (townId != null) {
                orderData.put("townId", townId);
            }

            orderData.put("address", order.getConsigneeDetail());

            // 发票信息
            orderData.put("invoiceState", 1001);
            if (Boolean.TRUE.equals(order.getNeedReceipt())) {
                orderData.put("invoiceState", 1002);
                orderData.put("invoiceType", 2);

                Map<String, Object> orderInvoice = new HashMap<>();
                // 注意：Order 类中没有 receiptTitle, receiptContent, taxpayerId 字段
                // 这些字段可能在 Trade 或其他地方，暂时设置为默认值
                orderInvoice.put("invoiceTitle", "个人");
                orderInvoice.put("invoiceContent", "商品明细");
                orderInvoice.put("invoiceSubjectType", 1002);

                // 如果有税号字段，可以在这里设置
                // orderInvoice.put("invoiceTaxNo", order.getTaxpayerId());

                orderData.put("orderInvoice", orderInvoice);
            }

            // 商品列表
            List<Map<String, Object>> goodsSkuList = buildGoodsSkuListFromItems(yzhOrderItems);
            orderData.put("goodsSkuList", goodsSkuList);

            // 订单买家留言
            if (order.getRemark() != null && !order.getRemark().isEmpty()) {
                orderData.put("buyerMsg", order.getRemark());
            }

            log.info("构建云中鹤订单数据完成，商品数量: {}, 订单金额: {}", goodsSkuList.size(), orderData.get("orderAmount"));
            return orderData;

        } catch (Exception e) {
            log.error("构建云中鹤订单数据失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 调用云中鹤创建订单接口
     *
     * @param authToken 认证Token
     * @param orderData 订单数据
     * @return 三方订单ID
     * <AUTHOR>
     */
    @Override
    protected String callThirdPartyCreateOrder(String authToken, Object orderData) {
        try {
            log.info("开始调用云中鹤创建订单接口");

            // 转换请求数据
            Map<String, Object> requestData = convertToYzhOrderRequest(authToken, orderData);
            if (requestData == null) {
                log.error("转换云中鹤订单请求数据失败");
                return null;
            }

            log.info("调用云中鹤创建订单接口，URL: {}", tpiProperties.getCreateOrderUrl());
            log.info("订单基本信息: 客户订单号={}, 订单金额={}, 收货人={}",
                    requestData.get("tparOrderCode"), requestData.get("orderAmount"), requestData.get("receiveName"));

            // 调用云中鹤创建订单接口
            String response = plus.qdt.common.utils.HttpUtils.doPostWithJson(tpiProperties.getCreateOrderUrl(), requestData);

            if (response == null || "error".equals(response)) {
                log.error("云中鹤创建订单接口调用失败");
                return null;
            }

            log.info("云中鹤创建订单接口响应: {}", response);

            // 解析响应结果
            return parseCreateOrderResponse(response);

        } catch (Exception e) {
            log.error("调用云中鹤创建订单接口失败: {}", e.getMessage(), e);
            return null;
        }
    }

    // ==================== 私有工具方法 ====================

    /**
     * 转换为云中鹤订单请求数据
     *
     * @param authToken 认证Token
     * @param orderData 原始订单数据
     * @return 云中鹤API请求数据
     * <AUTHOR>
     */
    private Map<String, Object> convertToYzhOrderRequest(String authToken, Object orderData) {
        Map<String, Object> sourceData = (Map<String, Object>) orderData;
        Map<String, Object> requestData = new HashMap<>(sourceData);

        // 添加认证令牌
        requestData.put("accessToken", authToken);

        // 确保goodsSkuList包含正确的字段格式
        List<Map<String, Object>> goodsSkuList = (List<Map<String, Object>>) sourceData.get("goodsSkuList");
        if (goodsSkuList != null && !goodsSkuList.isEmpty()) {
            // 验证并确保每个商品项包含必需字段
            for (Map<String, Object> goodsSkuItem : goodsSkuList) {
                // 确保goodsSkuCode字段存在且不为空
                if (!goodsSkuItem.containsKey("goodsSkuCode") || goodsSkuItem.get("goodsSkuCode") == null) {
                    log.warn("商品SKU项缺少goodsSkuCode字段: {}", goodsSkuItem);
                }
                // 确保sellPrice字段存在且为Double类型
                if (!goodsSkuItem.containsKey("sellPrice") || goodsSkuItem.get("sellPrice") == null) {
                    log.warn("商品SKU项缺少sellPrice字段: {}", goodsSkuItem);
                }
                // 确保sellQty字段存在且为Integer类型
                if (!goodsSkuItem.containsKey("sellQty") || goodsSkuItem.get("sellQty") == null) {
                    log.warn("商品SKU项缺少sellQty字段: {}", goodsSkuItem);
                }
            }
            requestData.put("goodsSkuList", goodsSkuList);
            log.debug("云中鹤订单商品列表包含{}个商品项", goodsSkuList.size());
        } else {
            log.warn("云中鹤订单请求数据中缺少goodsSkuList或为空");
        }

        // 复制发票相关字段到根级别（云中鹤API要求）
        Integer invoiceState = (Integer) sourceData.get("invoiceState");
        if (invoiceState != null && invoiceState == 1002) {
            Map<String, Object> orderInvoice = (Map<String, Object>) sourceData.get("orderInvoice");
            if (orderInvoice != null) {
                requestData.put("invoiceTitle", orderInvoice.get("invoiceTitle"));
                requestData.put("invoiceContent", orderInvoice.get("invoiceContent"));
                requestData.put("invoiceSubjectType", orderInvoice.get("invoiceSubjectType"));
                requestData.put("invoiceTaxNo", orderInvoice.get("invoiceTaxNo"));
            }
        }

        return requestData;
    }

    /**
     * 解析云中鹤创建订单响应
     *
     * @param response 响应字符串
     * @return 订单ID，失败返回null
     * <AUTHOR>
     */
    private String parseCreateOrderResponse(String response) {
        try {
            JSONObject jsonResponse = com.alibaba.fastjson2.JSONObject.parseObject(response);

            // 检查接口调用是否成功
            Boolean success = jsonResponse.getBoolean("success");
            if (success == null || !success) {
                String code = jsonResponse.getString("code");
                String desc = jsonResponse.getString("desc");
                log.error("云中鹤创建订单失败，错误码: {}, 错误描述: {}", code, desc);
                return null;
            }

            // 从result中获取父订单编号
            com.alibaba.fastjson2.JSONObject result = jsonResponse.getJSONObject("result");
            if (result != null && result.containsKey("parentOrderCode")) {
                String parentOrderCode = result.getString("parentOrderCode");
                log.info("云中鹤订单创建成功，父订单编号: {}", parentOrderCode);
                return parentOrderCode;
            } else {
                log.error("云中鹤创建订单响应中未找到parentOrderCode字段: {}", response);
                return null;
            }

        } catch (Exception e) {
            log.error("解析云中鹤创建订单响应失败: {}", e.getMessage(), e);
            return null;
        }
    }

    // ==================== 私有工具方法 ====================

    /**
     * 过滤出云中鹤订单项
     */
    private List<OrderItem> filterYzhOrderItems(List<OrderItem> orderItems) {
        return YzhOrderItemFilter.filterYzhOrderItems(orderItems);
    }

    /**
     * 计算订单金额（基于订单项）
     *
     * @param orderItems 订单项列表
     * @return 订单金额
     */
    private double calculateOrderAmountFromItems(List<OrderItem> orderItems) {
        return orderItems.stream()
                .mapToDouble(item -> item.getUnitPrice() * item.getNum())
                .sum();
    }

    /**
     * 构建商品SKU列表（基于订单项）
     *
     * @param orderItems 订单项列表
     * @return 商品SKU列表
     */
    private List<Map<String, Object>> buildGoodsSkuListFromItems(List<OrderItem> orderItems) {
        return orderItems.stream()
                .map(this::buildGoodsSkuItemFromOrderItem)
                .collect(Collectors.toList());
    }

    /**
     * 构建单个商品SKU项数据（基于订单项）
     *
     * @param orderItem 订单项
     * @return 商品SKU项数据
     */
    private Map<String, Object> buildGoodsSkuItemFromOrderItem(OrderItem orderItem) {
        Map<String, Object> goodsSkuItem = new HashMap<>();
        goodsSkuItem.put("goodsSkuCode", orderItem.getSupplierSkuId());

        // 通过supplierSkuId直接查询Goods获取platformPrice
        Double sellPrice = orderItem.getUnitPrice(); // 默认使用订单项的单价
        try {
            if (orderItem.getSupplierSkuId() != null) {
                // 构建查询参数，通过supplierGoodsId查询Goods
                GoodsSearchParams searchParams = new GoodsSearchParams();
                searchParams.setSupplierGoodsId(orderItem.getSupplierSkuId());

                Goods goods = goodsClient.getGoodsByParams(searchParams);
                if (goods != null && goods.getPlatformPrice() != null) {
                    sellPrice = goods.getPlatformPrice();
                    log.debug("从Goods获取平台价格，SKU: {}, 平台价格: {}",
                             orderItem.getSupplierSkuId(), sellPrice);
                } else {
                    log.warn("未找到对应的Goods或平台价格为空，SKU: {}, 使用订单项单价: {}",
                            orderItem.getSupplierSkuId(), sellPrice);
                }
            }
        } catch (Exception e) {
            log.error("查询Goods平台价格失败，SKU: {}, 使用订单项单价: {}, 异常: {}",
                    orderItem.getSupplierSkuId(), sellPrice, e.getMessage());
        }

        goodsSkuItem.put("sellPrice", sellPrice);
        goodsSkuItem.put("sellQty", orderItem.getNum());
        return goodsSkuItem;
    }

    /**
     * 解析地址ID并转换为云中鹤地址编码
     *
     * @param order 订单
     * @param index 地址级别索引（0:省, 1:市, 2:区, 3:街道）
     * @param levelName 级别名称（用于日志）
     * @return 云中鹤地址编码，找不到映射时返回null
     */
    private Integer parseAddressId(Order order, int index, String levelName) {
        String addressIdPath = order.getConsigneeAddressIdPath();
        if (addressIdPath != null && !addressIdPath.trim().isEmpty()) {
            String[] addressIds = addressIdPath.split(",");
            if (addressIds.length > index) {
                try {
                    String systemAddressId = addressIds[index];
                    // 调用 OpenFeign 接口转换地址
                    String yzhAddressCode = convertSystemAddressToYzh(systemAddressId, index + 1, levelName);
                    if (yzhAddressCode != null) {
                        return Integer.parseInt(yzhAddressCode);
                    }
                } catch (NumberFormatException e) {
                    log.warn("解析{}ID失败: {}", levelName, addressIds[index]);
                } catch (Exception e) {
                    log.error("调用地址转换服务失败，{}ID: {}, 异常: {}", levelName, addressIds[index], e.getMessage());
                }
            }
        }
        log.debug("{}地址转换结果为null，该字段不参与构建云中鹤请求数据", levelName);
        return null;
    }

    /**
     * 调用系统服务转换地址
     *
     * @param systemAddressId 系统地址ID
     * @param level 地址层级（1:省, 2:市, 3:区, 4:街道）
     * @param levelName 级别名称（用于日志）
     * @return 云中鹤地址编码
     */
    private String convertSystemAddressToYzh(String systemAddressId, int level, String levelName) {
        try {
            log.debug("开始转换{}地址，系统地址ID: {}, 层级: {}", levelName, systemAddressId, level);

            ResultMessage<String> result = thirdPartyAddressClient.getYzhAddressCodeByLevel(systemAddressId, level, "wnm");

            if (result != null && result.isSuccess() && result.getResult() != null) {
                String yzhCode = result.getResult();
                log.debug("{}地址转换成功，系统地址ID: {} -> 云中鹤编码: {}", levelName, systemAddressId, yzhCode);
                return yzhCode;
            } else {
                log.warn("{}地址转换失败，系统地址ID: {}, 响应: {}", levelName, systemAddressId,
                        result != null ? result.getMessage() : "null");
                return null;
            }

        } catch (Exception e) {
            log.error("调用地址转换服务异常，{}地址ID: {}, 异常: {}", levelName, systemAddressId, e.getMessage(), e);
            return null;
        }
    }

    private Integer parseProvinceIdFromOrder(Order order) {
        return parseAddressId(order, 0, "省份");
    }

    private Integer parseCityIdFromOrder(Order order) {
        return parseAddressId(order, 1, "城市");
    }

    private Integer parseCountyIdFromOrder(Order order) {
        return parseAddressId(order, 2, "区县");
    }

    private Integer parseTownIdFromOrder(Order order) {
        return parseAddressId(order, 3, "街道");
    }

    /**
     * 后置处理（基于订单）
     *
     * @param order 订单
     * @param thirdPartyOrderId 三方订单ID
     * @param orderItems 订单项
     */
    @Override
    protected void postProcess(Order order, String thirdPartyOrderId, List<OrderItem> orderItems) {
        try {
            log.info("开始保存云中鹤订单数据，系统订单号: {}, 云中鹤订单ID: {}", order.getSn(), thirdPartyOrderId);

            // 过滤出云中鹤商品
            List<OrderItem> yzhOrderItems = filterYzhOrderItems(orderItems);

            // 创建三方订单记录
            TpiOrderRecord tpiOrderRecord = buildTpiOrderRecord(order, thirdPartyOrderId, yzhOrderItems);

            // 保存到数据库
            int insertResult = tpiOrderRecordMapper.insert(tpiOrderRecord);
            if (insertResult > 0) {
                log.info("云中鹤订单数据保存成功，订单ID: {}, 商品数量: {}, 记录ID: {}",
                        thirdPartyOrderId, yzhOrderItems.size(), tpiOrderRecord.getId());
            } else {
                log.error("云中鹤订单数据保存失败，订单ID: {}, 商品数量: {}", thirdPartyOrderId, yzhOrderItems.size());
            }

        } catch (Exception e) {
            log.error("保存云中鹤订单数据失败，系统订单号: {}, 云中鹤订单ID: {}, 异常: {}",
                    order.getSn(), thirdPartyOrderId, e.getMessage(), e);
        }
    }

    /**
     * 构建三方订单记录（基于订单）
     *
     * @param order 订单
     * @param thirdPartyOrderId 三方订单ID
     * @param yzhOrderItems 云中鹤订单项
     * @return 三方订单记录
     */
    private TpiOrderRecord buildTpiOrderRecord(Order order, String thirdPartyOrderId, List<OrderItem> yzhOrderItems) {
        TpiOrderRecord record = new TpiOrderRecord();

        // 基本信息
        record.setSystemOrderSn(order.getSn());
        record.setThirdPartyOrderId(thirdPartyOrderId);
        record.setThirdPartyParentOrderCode(thirdPartyOrderId); // 云中鹤返回的是父订单编号
        record.setSupplierType("YZH");
        record.setOrderStatus(TpiOrderRecord.OrderStatus.SUCCESS.name());

        // 金额信息
        double orderAmount = calculateOrderAmountFromItems(yzhOrderItems);
        record.setOrderAmount(BigDecimal.valueOf(orderAmount));
        record.setFreightAmount(order.getFreightPrice() != null ? BigDecimal.valueOf(order.getFreightPrice()) : BigDecimal.ZERO);

        // 商品数量
        record.setGoodsCount(yzhOrderItems.size());

        // 时间信息
        record.setLastSyncTime(LocalDateTime.now());
        record.setRetryCount(0);

        return record;
    }

    // ==================== 取消订单实现方法 ====================

    /**
     * 取消前置验证
     * 验证订单和三方订单ID的有效性
     *
     * @param order 系统订单
     * @param thirdPartyOrderId 三方订单ID
     * @return 验证结果
     * <AUTHOR>
     */
    @Override
    protected boolean preCancelValidate(Order order, String thirdPartyOrderId) {
        // 1. 检查配置
        if (tpiProperties.getAppKey() == null || tpiProperties.getAppSecret() == null) {
            log.error("云中鹤配置不完整：appKey或appSecret为空");
            return false;
        }

        // 2. 检查订单信息
        if (order == null || order.getSn() == null) {
            log.error("系统订单信息不能为空");
            return false;
        }

        // 3. 检查三方订单ID
        if (thirdPartyOrderId == null || thirdPartyOrderId.trim().isEmpty()) {
            log.error("三方订单ID不能为空");
            return false;
        }

        // 4. 检查订单状态是否可以取消
        // 这里可以根据业务需求添加订单状态检查逻辑

        log.info("云中鹤取消订单前置验证通过，系统订单号: {}, 三方订单ID: {}", order.getSn(), thirdPartyOrderId);
        return true;
    }

    /**
     * 构建取消订单数据
     * 将系统订单数据转换为三方取消接口所需的格式
     *
     * @param order 系统订单
     * @param thirdPartyOrderId 三方订单ID
     * @return 取消订单数据
     * <AUTHOR>
     */
    @Override
    protected Object buildCancelOrderData(Order order, String thirdPartyOrderId) {
        try {
            log.info("构建云中鹤取消订单数据，系统订单号: {}, 三方订单ID: {}", order.getSn(), thirdPartyOrderId);

            // 构建取消订单请求参数
            // 根据API文档，至少需要传递一个订单编号参数
            YzhCancelOrderRequestParam requestParam = YzhCancelOrderRequestParam.build(
                    null, // accessToken 将在调用接口时设置
                    order.getSn(), // 客户方订单编号
                    thirdPartyOrderId // 云中鹤父订单编号
            );

            log.info("云中鹤取消订单数据构建完成");
            return requestParam;

        } catch (Exception e) {
            log.error("构建云中鹤取消订单数据失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 调用三方取消订单接口
     * 实际调用三方系统的订单取消接口
     *
     * @param authToken 认证令牌
     * @param cancelOrderData 取消订单数据
     * @return 取消结果状态
     * <AUTHOR>
     */
    @Override
    protected String callThirdPartyCancelOrder(String authToken, Object cancelOrderData) {
        try {
            log.info("开始调用云中鹤取消订单接口");

            // 转换请求数据
            YzhCancelOrderRequestParam requestParam = (YzhCancelOrderRequestParam) cancelOrderData;
            requestParam.setAccessToken(authToken);

            log.info("调用云中鹤取消订单接口，URL: {}", tpiProperties.getCancelOrderUrl());
            log.info("取消订单参数: 客户订单号={}, 父订单编号={}",
                    requestParam.getTparOrderCode(), requestParam.getParentOrderCode());

            // 调用云中鹤取消订单接口
            String response = HttpUtils.doPostWithJson(tpiProperties.getCancelOrderUrl(), requestParam);

            if (response == null || "error".equals(response)) {
                log.error("云中鹤取消订单接口调用失败");
                return null;
            }

            log.info("云中鹤取消订单接口响应: {}", response);

            // 解析响应结果
            return parseCancelOrderResponse(response);

        } catch (Exception e) {
            log.error("调用云中鹤取消订单接口失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 取消后置处理
     * 订单取消成功后的处理逻辑，如更新订单记录状态等
     *
     * @param order 系统订单
     * @param thirdPartyOrderId 三方订单ID
     * @param cancelResult 取消结果
     * <AUTHOR>
     */
    @Override
    protected void postCancelProcess(Order order, String thirdPartyOrderId, String cancelResult) {
        try {
            log.info("开始处理云中鹤取消订单后置逻辑，系统订单号: {}, 三方订单ID: {}, 取消结果: {}",
                    order.getSn(), thirdPartyOrderId, cancelResult);

            // 更新TpiOrderRecord状态
            updateTpiOrderRecordStatus(order.getSn(), thirdPartyOrderId, cancelResult);

            log.info("云中鹤取消订单后置处理完成");

        } catch (Exception e) {
            log.error("云中鹤取消订单后置处理失败，系统订单号: {}, 三方订单ID: {}, 异常: {}",
                    order.getSn(), thirdPartyOrderId, e.getMessage(), e);
        }
    }

    // ==================== 取消订单私有工具方法 ====================

    /**
     * 解析云中鹤取消订单响应
     *
     * @param response 响应字符串
     * @return 取消结果状态，失败返回null
     * <AUTHOR>
     */
    private String parseCancelOrderResponse(String response) {
        try {
            YzhCancelOrderResponse cancelResponse = com.alibaba.fastjson2.JSONObject.parseObject(response, YzhCancelOrderResponse.class);

            if (cancelResponse == null) {
                log.error("解析云中鹤取消订单响应失败：响应为空");
                return null;
            }

            // 检查接口调用是否成功
            if (!Boolean.TRUE.equals(cancelResponse.getSuccess())) {
                log.error("云中鹤取消订单失败，错误码: {}, 错误描述: {}",
                        cancelResponse.getCode(), cancelResponse.getDesc());
                return null;
            }

            // 检查业务结果
            if (cancelResponse.getResult() == null) {
                log.error("云中鹤取消订单响应中未找到result字段");
                return null;
            }

            Integer cancelStatus = cancelResponse.getResult().getCancelStatus();
            if (cancelStatus == null) {
                log.error("云中鹤取消订单响应中未找到cancelStatus字段");
                return null;
            }

            // 根据取消状态返回结果
            String resultStatus;
            switch (cancelStatus) {
                case 1000:
                    resultStatus = "SUCCESS"; // 取消成功
                    log.info("云中鹤订单取消成功，父订单编号: {}", cancelResponse.getResult().getParentOrderCode());
                    break;
                case 1010:
                    resultStatus = "PENDING"; // 取消审核中
                    log.info("云中鹤订单取消审核中，父订单编号: {}", cancelResponse.getResult().getParentOrderCode());
                    break;
                default:
                    resultStatus = "FAILED"; // 其他状态视为失败
                    log.warn("云中鹤订单取消状态未知: {}, 父订单编号: {}",
                            cancelStatus, cancelResponse.getResult().getParentOrderCode());
                    break;
            }

            return resultStatus;

        } catch (Exception e) {
            log.error("解析云中鹤取消订单响应失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 更新TpiOrderRecord状态
     *
     * @param systemOrderSn 系统订单号
     * @param thirdPartyOrderId 三方订单ID
     * @param cancelResult 取消结果
     * <AUTHOR>
     */
    private void updateTpiOrderRecordStatus(String systemOrderSn, String thirdPartyOrderId, String cancelResult) {
        try {
            // 查询现有记录
            TpiOrderRecord existingRecord = tpiOrderRecordMapper.selectOne(
                    new LambdaQueryWrapper<TpiOrderRecord>()
                            .eq(TpiOrderRecord::getSystemOrderSn, systemOrderSn)
                            .eq(TpiOrderRecord::getThirdPartyOrderId, thirdPartyOrderId)
                            .eq(TpiOrderRecord::getSupplierType, "YZH")
            );

            if (existingRecord != null) {
                // 更新状态
                String newStatus;
                switch (cancelResult) {
                    case "SUCCESS":
                        newStatus = TpiOrderRecord.OrderStatus.CANCELLED.name();
                        break;
                    case "PENDING":
                        newStatus = "CANCEL_PENDING"; // 取消审核中
                        break;
                    default:
                        newStatus = TpiOrderRecord.OrderStatus.FAILED.name();
                        break;
                }

                existingRecord.setOrderStatus(newStatus);
                existingRecord.setLastSyncTime(LocalDateTime.now());

                int updateResult = tpiOrderRecordMapper.updateById(existingRecord);
                if (updateResult > 0) {
                    log.info("TpiOrderRecord状态更新成功，订单号: {}, 新状态: {}", systemOrderSn, newStatus);
                } else {
                    log.error("TpiOrderRecord状态更新失败，订单号: {}", systemOrderSn);
                }
            } else {
                log.warn("未找到对应的TpiOrderRecord记录，系统订单号: {}, 三方订单ID: {}",
                        systemOrderSn, thirdPartyOrderId);
            }

        } catch (Exception e) {
            log.error("更新TpiOrderRecord状态失败，系统订单号: {}, 异常: {}", systemOrderSn, e.getMessage(), e);
        }
    }
}
