package plus.qdt.modules.order.cart.render.impl;

import cn.hutool.json.JSONUtil;
import plus.qdt.common.enums.SwitchEnum;
import plus.qdt.common.utils.CurrencyUtil;
import plus.qdt.modules.distribution.client.DistributionClient;
import plus.qdt.modules.distribution.client.DistributionGoodsClient;
import plus.qdt.modules.distribution.entity.dos.Distribution;
import plus.qdt.modules.distribution.entity.dos.DistributionBind;
import plus.qdt.modules.distribution.entity.dos.DistributionGoods;
import plus.qdt.modules.distribution.entity.dos.DistributionStore;
import plus.qdt.modules.distribution.entity.enums.DistributionModelEnum;
import plus.qdt.modules.distribution.entity.vos.DistributionVO;
import plus.qdt.modules.order.cart.entity.dto.TradeDTO;
import plus.qdt.modules.order.cart.entity.enums.RenderStepEnums;
import plus.qdt.modules.order.cart.entity.vo.CartVO;
import plus.qdt.modules.order.cart.render.CartRenderStep;
import plus.qdt.modules.order.order.entity.dto.PriceDetailDTO;
import plus.qdt.modules.system.client.SettingClient;
import plus.qdt.modules.system.entity.dos.Setting;
import plus.qdt.modules.system.entity.dto.DistributionSetting;
import plus.qdt.modules.system.entity.enums.SettingEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 分销佣金计算
 *
 * <AUTHOR>
 * @since 2020-07-02 14:47
 */
@Service
@RequiredArgsConstructor
public class DistributionPriceRender implements CartRenderStep {

    private final SettingClient settingClient;
    private final DistributionClient distributionClient;
    private final DistributionGoodsClient distributionGoodsClient;

    @Override
    public RenderStepEnums step() {
        return RenderStepEnums.DISTRIBUTION;
    }

    @Override
    public void render(TradeDTO tradeDTO) {
        this.renderDistribution(tradeDTO);
    }

    /**
     * 渲染分销佣金
     *
     * @param tradeDTO 购物车信息
     */
    private void renderDistribution(TradeDTO tradeDTO) {

        // 获取分销配置
        Setting setting = settingClient.get(SettingEnum.DISTRIBUTION_SETTING.name());
        DistributionSetting distributionSetting = JSONUtil.toBean(setting.getSettingValue(), DistributionSetting.class);

        if (SwitchEnum.OPEN.equals(distributionSetting.getIsOpen())) {
            //如果存在分销员 才进行运算
            List<DistributionVO> distributions = distributionClient.getUpDistributionBind(tradeDTO.getMemberId());
            if (distributions == null || distributions.isEmpty()) {
                return;
            }

            // 循环购物车
            for (CartVO cartVO : tradeDTO.getCartList()) {

                // 一二级分销元佣金比例
                Double firstCommission;
                Double secondaryCommission;
                // 如果是店铺分销
                if (distributionSetting.getCommissionModel().equals(DistributionModelEnum.SELLER)) {
                    DistributionStore distributionStore = distributionClient.getByStoreId(cartVO.getStoreId());
                    if (distributionStore == null) {
                        continue;
                    }
                    firstCommission = distributionStore.getCommission();
                    secondaryCommission = distributionStore.getSecondaryCommission();
                } else {
                    firstCommission = distributionSetting.getFirstCommission();
                    secondaryCommission = distributionSetting.getSecondaryCommission();
                }

                // 写入分销员信息
                cartVO.setDistribution1(distributions.get(0));
                if (distributions.size() > 1) {
                    cartVO.setDistribution2(distributions.get(1));
                }

                // 如果佣金比例都为 0，则直接跳过
                if (firstCommission == 0.0 && secondaryCommission == 0.0) {
                    continue;
                }
                // 渲染分销佣金
                this.renderSkuPrice(cartVO, firstCommission, secondaryCommission, distributionSetting);
            }
        }
    }

    /**
     * 渲染分销佣金
     *
     * @param cartVO              购物车信息
     * @param firstCommission     一级分销佣金
     * @param secondaryCommission 二级分销佣金
     * @param distributionSetting 分销配置
     */
    private void renderSkuPrice(CartVO cartVO, Double firstCommission, Double secondaryCommission,
                                DistributionSetting distributionSetting) {

        // 判定特殊条件
        // 如果一级分销员为空，且佣金比例为 0，并且 二级分销员为空，且佣金比例为 0，则直接跳过
        if ((cartVO.getDistribution1() == null && firstCommission.equals(0.0)) && (cartVO.getDistribution2() == null
                && secondaryCommission.equals(0.0))) {
            return;
        }
        // 循环购物车商品 进行佣金计算
        cartVO.getCheckedSkuList().forEach(cartSkuVO -> {
            PriceDetailDTO priceDetailDTO = cartSkuVO.getPriceDetailDTO();
            // 佣金承担人
            priceDetailDTO.setCommissionModel(distributionSetting.getCommissionModel().name());
            // 一级分销佣金
            if (cartVO.getDistribution1() != null) {
                priceDetailDTO.setFirstCommissionRate(firstCommission);
            }
            // 二级分销佣金
            if (cartVO.getDistribution2() != null) {
                priceDetailDTO.setSecondaryCommissionRate(secondaryCommission);
            }
        });
    }
}
