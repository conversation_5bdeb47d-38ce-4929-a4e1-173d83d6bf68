package plus.qdt.modules.order.tpi.service.impl;

import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.tpi.yzh.TpiHttpUtils;
import plus.qdt.common.tpi.yzh.config.YzhTpiProperties;
import plus.qdt.common.utils.HttpUtils;
import plus.qdt.modules.goods.client.GoodsClient;
import plus.qdt.modules.goods.entity.dos.Goods;
import plus.qdt.modules.goods.entity.dos.GoodsSku;
import plus.qdt.modules.goods.entity.dto.GoodsSearchParams;
import plus.qdt.modules.goods.entity.dto.YZHGoodsSkuDTO;
import plus.qdt.modules.goods.entity.dto.YZHRequestResultDTO;
import plus.qdt.modules.goods.entity.enums.GoodsMarketEnum;
import plus.qdt.modules.supplier.entity.dto.SupplierGoodsOperationDTO;
import com.alibaba.fastjson2.JSONArray;
import plus.qdt.modules.order.tpi.service.YzhMessageService;
import plus.qdt.modules.system.entity.dto.YzhQueryMsgListRequestParam;
import plus.qdt.modules.system.entity.dto.YzhQueryMsgListResponse;
import plus.qdt.modules.system.entity.dto.YzhDeleteMsgRequestParam;
import plus.qdt.modules.system.entity.dto.YzhDeleteMsgResponse;
import plus.qdt.modules.system.entity.enums.YzhMessageSmallTypeEnum;
import plus.qdt.modules.system.entity.enums.YzhMessageTypeEnum;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 云中鹤消息服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Slf4j
@Service
public class YzhMessageServiceImpl implements YzhMessageService {

    @Autowired
    private TpiHttpUtils tpiHttpUtils;

    @Autowired
    private YzhTpiProperties tpiProperties;

    @Autowired
    private GoodsClient goodsClient;

    @Override
    public YzhQueryMsgListResponse queryMessageList(YzhQueryMsgListRequestParam requestParam) {
        log.info("开始查询云中鹤消息列表，消息类型: {}, 页码: {}, 每页条数: {}",
                requestParam.getMessageType(), requestParam.getPageNum(), requestParam.getPageSize());

        try {
            // 1. 获取认证Token
            String accessToken = tpiHttpUtils.getToken();

            // 2. 设置认证Token
            requestParam.setAccessToken(accessToken);

            // 3. 设置默认分页参数
            if (requestParam.getPageNum() == null || requestParam.getPageNum() < 1) {
                requestParam.setPageNum(1);
            }
            if (requestParam.getPageSize() == null || requestParam.getPageSize() < 1) {
                requestParam.setPageSize(100);
            }

            // 4. 调用云中鹤查询消息列表接口
            YzhQueryMsgListResponse response = callYzhQueryMsgList(requestParam);

            // 5. 验证响应结果
            validateQueryMsgListResponse(response);

            // 6. 处理消息列表中的消息
            processMessages(response);

            log.info("云中鹤消息列表查询完成，消息类型: {}, 总条数: {}",
                    requestParam.getMessageType(),
                    response.getResult() != null ? response.getResult().getTotalCount() : 0);

            // 删除已处理的消息
            if (response.getResult() != null && response.getResult().getList() != null && !response.getResult().getList().isEmpty()) {
                try {
                    YzhDeleteMsgResponse deleteResponse = deleteProcessedMessages(response.getResult().getList());
                    if (deleteResponse != null && Boolean.TRUE.equals(deleteResponse.getSuccess())) {
                        log.info("删除已处理消息成功，消息数量: {}", response.getResult().getList().size());
                    } else {
                        log.warn("删除已处理消息失败: {}", deleteResponse != null ? deleteResponse.getDesc() : "未知错误");
                    }
                } catch (Exception e) {
                    log.error("删除已处理消息时发生异常，消息数量: {}, 错误: {}",
                            response.getResult().getList().size(), e.getMessage(), e);
                }
            }

            return response;

        } catch (Exception e) {
            log.error("查询云中鹤消息列表失败，消息类型: {}, 错误: {}", requestParam.getMessageType(), e.getMessage(), e);
            throw new ServiceException("查询云中鹤消息列表失败: " + e.getMessage());
        }
    }

    @Override
    public void processMessages(YzhQueryMsgListResponse response) {
        if (response == null || response.getResult() == null || response.getResult().getList() == null) {
            log.warn("消息列表为空，无需处理");
            return;
        }

        List<YzhQueryMsgListResponse.YzhMessage> messages = response.getResult().getList();
        log.info("开始处理消息列表，消息数量: {}", messages.size());

        // 使用stream流按消息类型分组处理
        Map<Integer, List<YzhQueryMsgListResponse.YzhMessage>> messageGroups = messages.stream()
                .collect(Collectors.groupingBy(YzhQueryMsgListResponse.YzhMessage::getMessageType));

        // 分别处理不同类型的消息
        messageGroups.forEach((messageType, messageList) -> {
            try {
                processMessagesByType(messageType, messageList);
            } catch (Exception e) {
                log.error("处理消息类型 {} 失败，消息数量: {}, 错误: {}", messageType, messageList.size(), e.getMessage(), e);
            }
        });

        log.info("消息列表处理完成，消息数量: {}", messages.size());
    }

    /**
     * 按消息类型批量处理消息
     *
     * @param messageType 消息类型
     * @param messageList 同类型消息列表
     */
    private void processMessagesByType(Integer messageType, List<YzhQueryMsgListResponse.YzhMessage> messageList) {
        YzhMessageTypeEnum messageTypeEnum = YzhMessageTypeEnum.getByCode(messageType);
        if (messageTypeEnum == null) {
            log.warn("未知的消息类型: {}, 消息数量: {}", messageType, messageList.size());
            return;
        }

        log.info("开始批量处理 {} 消息，消息数量: {}", messageTypeEnum.getDescription(), messageList.size());

        switch (messageTypeEnum) {
            case GOODS_MESSAGE:
                processGoodsMessages(messageList);
                break;
            case SALES_ORDER_MESSAGE:
                processSalesOrderMessages(messageList);
                break;
            case AFTER_SALE_ORDER_MESSAGE:
                processAfterSaleOrderMessages(messageList);
                break;
            default:
                log.warn("未处理的消息类型: {}", messageTypeEnum);
        }

        log.info("批量处理 {} 消息完成，消息数量: {}", messageTypeEnum.getDescription(), messageList.size());
    }

    /**
     * 批量处理商品消息
     *
     * @param messageList 商品消息列表
     */
    private void processGoodsMessages(List<YzhQueryMsgListResponse.YzhMessage> messageList) {
        // 按商品消息小类进一步分组
        Map<Integer, List<YzhQueryMsgListResponse.YzhMessage>> goodsMessageGroups = messageList.stream()
                .collect(Collectors.groupingBy(YzhQueryMsgListResponse.YzhMessage::getMessageSmallType));

        // 按消息小类分组处理
        List<YzhQueryMsgListResponse.YzhMessage> priceChangeMessages = goodsMessageGroups.getOrDefault(YzhMessageSmallTypeEnum.GOODS_PRICE_CHANGE.getCode(), new ArrayList<>());
        List<YzhQueryMsgListResponse.YzhMessage> onShelfMessages = goodsMessageGroups.getOrDefault(YzhMessageSmallTypeEnum.GOODS_ON_SHELF.getCode(), new ArrayList<>());

        // 合并下架和停售消息（都是设置为DOWN状态）
        List<YzhQueryMsgListResponse.YzhMessage> downStatusMessages = new ArrayList<>();
        downStatusMessages.addAll(goodsMessageGroups.getOrDefault(YzhMessageSmallTypeEnum.GOODS_OFF_SHELF.getCode(), new ArrayList<>()));
        downStatusMessages.addAll(goodsMessageGroups.getOrDefault(YzhMessageSmallTypeEnum.GOODS_STOP_SALE.getCode(), new ArrayList<>()));

        // 批量处理各类型消息
        if (!downStatusMessages.isEmpty()) {
            log.info("批量处理商品下架/停售消息，总数量: {}", downStatusMessages.size());
            batchUpdateGoodsMarketStatus(downStatusMessages, GoodsMarketEnum.DOWN);
        }

        if (!priceChangeMessages.isEmpty()) {
            log.info("批量处理商品改价消息，数量: {}", priceChangeMessages.size());
            batchUpdateGoodsPrice(priceChangeMessages);
        }

        if (!onShelfMessages.isEmpty()) {
            log.info("批量处理商品上架消息，数量: {}", onShelfMessages.size());
            batchUpdateGoodsMarketStatus(onShelfMessages, GoodsMarketEnum.UPPER);
        }

        // 处理未知类型的消息
        goodsMessageGroups.entrySet().stream()
                .filter(entry -> YzhMessageSmallTypeEnum.getByCode(entry.getKey()) == null)
                .forEach(entry -> log.warn("未知的商品消息小类: {}, 消息数量: {}", entry.getKey(), entry.getValue().size()));
    }

    /**
     * 批量处理销售订单消息
     *
     * @param messageList 销售订单消息列表
     */
    private void processSalesOrderMessages(List<YzhQueryMsgListResponse.YzhMessage> messageList) {
        log.info("批量处理销售订单消息，消息数量: {}", messageList.size());

        // 按订单消息小类分组处理
        Map<Integer, List<YzhQueryMsgListResponse.YzhMessage>> orderMessageGroups = messageList.stream()
                .collect(Collectors.groupingBy(YzhQueryMsgListResponse.YzhMessage::getMessageSmallType));

        orderMessageGroups.forEach((smallType, messages) -> {
            YzhMessageSmallTypeEnum smallTypeEnum = YzhMessageSmallTypeEnum.getByCode(smallType);
            log.info("处理销售订单消息小类: {}, 消息数量: {}",
                    smallTypeEnum != null ? smallTypeEnum.getDescription() : smallType, messages.size());
            // TODO: 实现具体的销售订单处理逻辑
        });
    }

    /**
     * 批量处理售后订单消息
     *
     * @param messageList 售后订单消息列表
     */
    private void processAfterSaleOrderMessages(List<YzhQueryMsgListResponse.YzhMessage> messageList) {
        log.info("批量处理售后订单消息，消息数量: {}", messageList.size());

        // 按售后消息小类分组处理
        Map<Integer, List<YzhQueryMsgListResponse.YzhMessage>> afterSaleMessageGroups = messageList.stream()
                .collect(Collectors.groupingBy(YzhQueryMsgListResponse.YzhMessage::getMessageSmallType));

        afterSaleMessageGroups.forEach((smallType, messages) -> {
            YzhMessageSmallTypeEnum smallTypeEnum = YzhMessageSmallTypeEnum.getByCode(smallType);
            log.info("处理售后订单消息小类: {}, 消息数量: {}",
                    smallTypeEnum != null ? smallTypeEnum.getDescription() : smallType, messages.size());
            // TODO: 实现具体的售后订单处理逻辑
        });
    }


    @Override
    public void handleGoodsOffShelf(YzhQueryMsgListResponse.YzhMessage message) {
        updateGoodsMarketStatus(message.getGoodsSkuCode(), GoodsMarketEnum.DOWN);
    }

    @Override
    public void handleGoodsPriceChange(YzhQueryMsgListResponse.YzhMessage message) {
        if (message.getGoodsSkuCode() == null || message.getGoodsSkuCode().trim().isEmpty()) {
            log.warn("商品SKU编码为空，无法处理改价消息");
            return;
        }

        try {
            // 1. 调用云中鹤商品详情接口获取最新价格
            YZHGoodsSkuDTO goodsDetail = getGoodsDetailFromYzh(message.getGoodsSkuCode());
            if (goodsDetail == null) {
                log.warn("未能获取到商品详情信息，商品SKU: {}", message.getGoodsSkuCode());
                return;
            }

            // 2. 解析价格信息并更新商品价格
            BigDecimal newPrice = parsePrice(goodsDetail.getSellPrice());
            if (newPrice != null) {
                updateGoodsPrice(message.getGoodsSkuCode(), newPrice);
                log.info("商品改价完成，SKU: {}, 新价格: {}", message.getGoodsSkuCode(), newPrice);
            } else {
                log.warn("商品价格解析失败，SKU: {}, 价格: {}", message.getGoodsSkuCode(), goodsDetail.getSellPrice());
            }
        } catch (Exception e) {
            log.error("处理商品改价失败，SKU: {}, 错误: {}", message.getGoodsSkuCode(), e.getMessage(), e);
        }
    }

    @Override
    public void handleGoodsStopSale(YzhQueryMsgListResponse.YzhMessage message) {
        updateGoodsMarketStatus(message.getGoodsSkuCode(), GoodsMarketEnum.DOWN);
    }

    @Override
    public void handleGoodsOnShelf(YzhQueryMsgListResponse.YzhMessage message) {
        updateGoodsMarketStatus(message.getGoodsSkuCode(), GoodsMarketEnum.UPPER);
    }

    /**
     * 批量更新商品价格
     */
    private void batchUpdateGoodsPrice(List<YzhQueryMsgListResponse.YzhMessage> messages) {
        if (messages == null || messages.isEmpty()) {
            return;
        }

        log.info("开始批量处理商品改价，消息数量: {}", messages.size());

        // 1. 收集所有有效的SKU编码
        List<String> skuCodes = messages.stream()
                .map(YzhQueryMsgListResponse.YzhMessage::getGoodsSkuCode)
                .filter(skuCode -> skuCode != null && !skuCode.trim().isEmpty())
                .distinct() // 去重
                .toList();

        if (skuCodes.isEmpty()) {
            log.warn("没有有效的SKU编码，跳过商品改价处理");
            return;
        }

        log.info("收集到有效SKU编码数量: {}", skuCodes.size());

        // 2. 批量获取商品详情（每页25条）
        List<YZHGoodsSkuDTO> allGoodsDetails = batchGetGoodsDetailsFromYzh(skuCodes);
        if (allGoodsDetails.isEmpty()) {
            log.warn("未获取到任何商品详情信息");
            return;
        }

        log.info("批量获取商品详情成功，数量: {}", allGoodsDetails.size());

        // 3. 处理商品价格更新
        List<SupplierGoodsOperationDTO> goodsUpdateList = new ArrayList<>();
        List<GoodsSku> skuUpdateList = new ArrayList<>();

        for (YZHGoodsSkuDTO goodsDetail : allGoodsDetails) {
            try {
                // 解析价格信息
                BigDecimal newPrice = parsePrice(goodsDetail.getSellPrice());
                if (newPrice == null) {
                    log.warn("商品价格解析失败，SKU: {}, 价格: {}", goodsDetail.getGoodsSkuCode(), goodsDetail.getSellPrice());
                    continue;
                }

                // 查询本地商品信息
                GoodsSearchParams goodsSearchParams = new GoodsSearchParams();
                goodsSearchParams.setSupplierGoodsId(goodsDetail.getGoodsSkuCode());
                Goods goods = goodsClient.getGoodsByParams(goodsSearchParams);

                if (goods == null) {
                    log.warn("未找到商品记录，SKU: {}", goodsDetail.getGoodsSkuCode());
                    continue;
                }

                // 准备商品更新数据
                BigDecimal originalPrice = newPrice;
                SupplierGoodsOperationDTO supplierGoodsOperationDTO = new SupplierGoodsOperationDTO();
                supplierGoodsOperationDTO.setGoodsId(goods.getId());
                supplierGoodsOperationDTO.setPrice(newPrice.doubleValue());
                supplierGoodsOperationDTO.setOriginalPrice(originalPrice.doubleValue());
                goodsUpdateList.add(supplierGoodsOperationDTO);

                // 准备SKU更新数据
                GoodsSearchParams skuSearchParams = new GoodsSearchParams();
                skuSearchParams.setGoodsId(goods.getId());
                List<GoodsSku> goodsSkuList = goodsClient.getGoodsSkuByList(skuSearchParams);

                if (goodsSkuList != null && !goodsSkuList.isEmpty()) {
                    goodsSkuList.forEach(goodsSku -> {
                        goodsSku.setPrice(newPrice.doubleValue());
                        goodsSku.setOriginalPrice(originalPrice.doubleValue());
                        skuUpdateList.add(goodsSku);
                    });
                }

                log.debug("商品改价数据准备完成，SKU: {}, 新价格: {}", goodsDetail.getGoodsSkuCode(), newPrice);

            } catch (Exception e) {
                log.error("处理商品改价失败，SKU: {}, 错误: {}", goodsDetail.getGoodsSkuCode(), e.getMessage(), e);
            }
        }

        // 4. 批量更新商品信息
        if (!goodsUpdateList.isEmpty()) {
            log.info("开始批量更新商品信息，数量: {}", goodsUpdateList.size());
            batchUpdateGoods(goodsUpdateList);
        }

        // 5. 批量更新SKU信息
        if (!skuUpdateList.isEmpty()) {
            log.info("开始批量更新SKU信息，数量: {}", skuUpdateList.size());
            batchUpdateGoodsSku(skuUpdateList);
        }

        log.info("批量商品改价处理完成，处理商品数量: {}, 处理SKU数量: {}", goodsUpdateList.size(), skuUpdateList.size());
    }

    /**
     * 批量更新商品信息
     */
    private void batchUpdateGoods(List<SupplierGoodsOperationDTO> goodsUpdateList) {
        if (goodsUpdateList == null || goodsUpdateList.isEmpty()) {
            return;
        }

        log.info("开始批量更新商品信息，总数量: {}", goodsUpdateList.size());

        try {
            // 直接调用批量更新接口
            goodsClient.batchEditSupplierGoods(goodsUpdateList);
            log.info("批量商品信息更新成功，处理数量: {}", goodsUpdateList.size());
        } catch (Exception e) {
            log.error("批量商品更新失败，总数量: {}, 错误: {}, 降级为逐个处理", goodsUpdateList.size(), e.getMessage(), e);

            // 如果批量失败，降级为逐个更新
            int successCount = 0;
            for (SupplierGoodsOperationDTO goods : goodsUpdateList) {
                try {
                    goodsClient.editSupplierGoods(goods);
                    successCount++;
                } catch (Exception ex) {
                    log.error("单个商品更新失败，商品ID: {}, 错误: {}", goods.getGoodsId(), ex.getMessage());
                }
            }
            log.info("逐个商品更新完成，成功数量: {}, 失败数量: {}", successCount, goodsUpdateList.size() - successCount);
        }
    }

    /**
     * 批量更新商品SKU信息
     */
    private void batchUpdateGoodsSku(List<GoodsSku> skuUpdateList) {
        if (skuUpdateList == null || skuUpdateList.isEmpty()) {
            return;
        }

        log.info("开始批量更新SKU信息，总数量: {}", skuUpdateList.size());

        try {
            // 直接调用批量更新接口
            goodsClient.batchUpdateGoodsSku(skuUpdateList);
            log.info("批量SKU信息更新成功，处理数量: {}", skuUpdateList.size());
        } catch (Exception e) {
            log.error("批量SKU更新失败，总数量: {}, 错误: {}, 降级为逐个处理", skuUpdateList.size(), e.getMessage(), e);

            // 如果批量失败，降级为逐个更新
            int successCount = 0;
            for (GoodsSku sku : skuUpdateList) {
                try {
                    goodsClient.updateGoodsSku(sku);
                    successCount++;
                } catch (Exception ex) {
                    log.error("单个SKU更新失败，SKU ID: {}, 错误: {}", sku.getId(), ex.getMessage());
                }
            }
            log.info("逐个SKU更新完成，成功数量: {}, 失败数量: {}", successCount, skuUpdateList.size() - successCount);
        }
    }

    /**
     * 批量更新商品上下架状态
     */
    private void batchUpdateGoodsMarketStatus(List<YzhQueryMsgListResponse.YzhMessage> messages, GoodsMarketEnum marketStatus) {
        if (messages == null || messages.isEmpty()) {
            return;
        }

        log.info("开始批量更新商品状态，消息数量: {}, 目标状态: {}", messages.size(), marketStatus.name());

        // 收集有效的SKU编码
        List<String> validSkuCodes = messages.stream()
                .map(YzhQueryMsgListResponse.YzhMessage::getGoodsSkuCode)
                .filter(skuCode -> skuCode != null && !skuCode.trim().isEmpty())
                .toList();

        if (validSkuCodes.isEmpty()) {
            log.warn("没有有效的SKU编码，跳过批量状态更新");
            return;
        }

        // TODO: 实现批量更新商品状态的逻辑
        // 这里可以根据实际需求实现批量更新商品状态
        validSkuCodes.forEach(skuCode -> {
            try {
                log.info("更新商品状态，SKU: {}, 状态: {}", skuCode, marketStatus.name());
                // 实际的批量更新逻辑
            } catch (Exception e) {
                log.error("更新商品状态失败，SKU: {}, 状态: {}, 错误: {}", skuCode, marketStatus.name(), e.getMessage(), e);
            }
        });

        log.info("批量商品状态更新完成，处理数量: {}", validSkuCodes.size());
    }

    /**
     * 更新商品上下架状态
     */
    private void updateGoodsMarketStatus(String goodsSkuCode, GoodsMarketEnum marketStatus) {
        if (goodsSkuCode == null || goodsSkuCode.trim().isEmpty()) {
            log.warn("商品SKU编码为空，无法更新商品状态");
            return;
        }

        try {
            // TODO: 实现根据supplierSkuId查询和更新商品状态的逻辑
            log.info("更新商品状态，SKU: {}, 状态: {}", goodsSkuCode, marketStatus.name());
        } catch (Exception e) {
            log.error("更新商品状态失败，SKU: {}, 状态: {}, 错误: {}", goodsSkuCode, marketStatus.name(), e.getMessage(), e);
        }
    }

    /**
     * 调用云中鹤查询消息列表接口
     *
     * @param requestParam 请求参数
     * @return 响应结果
     */
    private YzhQueryMsgListResponse callYzhQueryMsgList(YzhQueryMsgListRequestParam requestParam) {
        log.info("调用云中鹤查询消息列表接口，URL: {}", tpiProperties.getMsgListUrl());

        Map<String, Object> params = new HashMap<>();
        params.put("accessToken", requestParam.getAccessToken());
        params.put("messageType", requestParam.getMessageType().toString());
        if (requestParam.getPageNum() != null) {
            params.put("pageNum", requestParam.getPageNum());
        }
        if (requestParam.getPageSize() != null) {
            params.put("pageSize", requestParam.getPageSize());
        }

        String responseStr = HttpUtils.doPostWithJson(tpiProperties.getMsgListUrl(), params);
        log.info("云中鹤查询消息列表接口响应: {}", responseStr);

        return JSONObject.parseObject(responseStr, YzhQueryMsgListResponse.class);
    }

    /**
     * 验证查询消息列表响应结果
     *
     * @param response 响应结果
     */
    private void validateQueryMsgListResponse(YzhQueryMsgListResponse response) {
        if (response == null) {
            throw new ServiceException("云中鹤查询消息列表返回空响应");
        }

        if (!Boolean.TRUE.equals(response.getSuccess()) || !"00000".equals(response.getCode())) {
            throw new ServiceException("云中鹤查询消息列表失败: " + response.getDesc());
        }

        if (response.getResult() == null) {
            throw new ServiceException("云中鹤查询消息列表返回空结果");
        }
    }

    /**
     * 批量从云中鹤获取商品详情
     */
    private List<YZHGoodsSkuDTO> batchGetGoodsDetailsFromYzh(List<String> skuCodes) {
        if (skuCodes == null || skuCodes.isEmpty()) {
            return new ArrayList<>();
        }

        List<YZHGoodsSkuDTO> allDetails = new ArrayList<>();
        int batchSize = 25; // 每批25个SKU

        try {
            // 获取AccessToken
            String accessToken = tpiHttpUtils.getToken();
            if (accessToken == null || accessToken.isEmpty()) {
                log.error("获取AccessToken失败，无法批量获取商品详情");
                return new ArrayList<>();
            }

            // 分批处理
            for (int i = 0; i < skuCodes.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, skuCodes.size());
                List<String> batchSkuCodes = skuCodes.subList(i, endIndex);

                log.info("获取第 {} 批商品详情，本批 {} 个SKU", (i / batchSize + 1), batchSkuCodes.size());

                List<YZHGoodsSkuDTO> batchDetails = fetchGoodsDetailBatch(accessToken, batchSkuCodes);
                allDetails.addAll(batchDetails);

                log.info("第 {} 批商品详情获取完成，返回 {} 个商品详情", (i / batchSize + 1), batchDetails.size());
            }

            log.info("批量获取商品详情完成，总请求SKU数量: {}, 总返回详情数量: {}", skuCodes.size(), allDetails.size());
            return allDetails;

        } catch (Exception e) {
            log.error("批量获取商品详情时发生异常，SKU数量: {}, 异常: {}", skuCodes.size(), e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取单批商品详情
     */
    private List<YZHGoodsSkuDTO> fetchGoodsDetailBatch(String accessToken, List<String> skuCodes) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("accessToken", accessToken);
            params.put("goodsSkuCode", skuCodes);

            // 发送HTTP请求
            String goodsDetailUrl = tpiProperties.getGoodsDetailUrl();
            String response = HttpUtils.doPostWithJson(goodsDetailUrl, JSONObject.toJSONString(params));

            if (response == null || response.isEmpty()) {
                log.error("商品详情获取失败，响应为空，SKU数量: {}", skuCodes.size());
                return new ArrayList<>();
            }

            // 解析响应数据
            YZHRequestResultDTO detailResponse;
            try {
                log.debug("商品详情响应内容，SKU数量: {}, 响应: {}", skuCodes.size(), response);
                detailResponse = JSONObject.parseObject(response, YZHRequestResultDTO.class);
            } catch (Exception e) {
                log.error("商品详情JSON解析失败，SKU数量: {}, 响应内容: {}, 异常: {}", skuCodes.size(), response, e.getMessage());
                return new ArrayList<>();
            }

            if (detailResponse == null || !Boolean.TRUE.equals(detailResponse.getSuccess())) {
                log.error("商品详情解析失败或接口返回失败: {}, SKU数量: {}",
                        detailResponse != null ? detailResponse.getDesc() : "解析失败", skuCodes.size());
                return new ArrayList<>();
            }

            // 处理result字段
            String resultStr = detailResponse.getResult();
            if (resultStr == null || resultStr.isEmpty()) {
                log.warn("商品详情结果为空，SKU数量: {}", skuCodes.size());
                return new ArrayList<>();
            }

            // 解析商品详情列表
            List<YZHGoodsSkuDTO> detailList;
            try {
                detailList = JSONArray.parseArray(resultStr, YZHGoodsSkuDTO.class);
            } catch (Exception e) {
                log.error("商品详情列表解析失败，SKU数量: {}, 结果: {}, 异常: {}", skuCodes.size(), resultStr, e.getMessage());
                return new ArrayList<>();
            }

            if (detailList == null) {
                detailList = new ArrayList<>();
            }

            log.info("批量获取商品详情成功，请求SKU数量: {}, 返回详情数量: {}", skuCodes.size(), detailList.size());
            return detailList;

        } catch (Exception e) {
            log.error("批量获取商品详情时发生异常，SKU数量: {}, 异常: {}", skuCodes.size(), e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 从云中鹤获取商品详情（单个）
     */
    private YZHGoodsSkuDTO getGoodsDetailFromYzh(String goodsSkuCode) {
        try {
            if (goodsSkuCode == null || goodsSkuCode.trim().isEmpty()) {
                log.warn("商品SKU编码为空，无法获取商品详情");
                return null;
            }

            // 1. 获取AccessToken
            String accessToken = tpiHttpUtils.getToken();
            if (accessToken == null || accessToken.isEmpty()) {
                log.error("获取AccessToken失败，无法获取商品详情");
                return null;
            }

            // 2. 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("accessToken", accessToken);
            params.put("goodsSkuCode", List.of(goodsSkuCode)); // 注意：参数是数组格式

            // 3. 发送HTTP请求
            String goodsDetailUrl = tpiProperties.getGoodsDetailUrl();
            String response = HttpUtils.doPostWithJson(goodsDetailUrl, JSONObject.toJSONString(params));

            if (response == null || response.isEmpty()) {
                log.error("商品详情获取失败，响应为空，SKU: {}", goodsSkuCode);
                return null;
            }

            // 4. 解析响应数据
            YZHRequestResultDTO detailResponse;
            try {
                log.debug("商品详情响应内容，SKU: {}, 响应: {}", goodsSkuCode, response);
                detailResponse = JSONObject.parseObject(response, YZHRequestResultDTO.class);
            } catch (Exception e) {
                log.error("商品详情JSON解析失败，SKU: {}, 响应内容: {}, 异常: {}", goodsSkuCode, response, e.getMessage());
                return null;
            }

            if (detailResponse == null || !Boolean.TRUE.equals(detailResponse.getSuccess())) {
                log.error("商品详情解析失败或接口返回失败: {}, SKU: {}",
                        detailResponse != null ? detailResponse.getDesc() : "解析失败", goodsSkuCode);
                return null;
            }

            // 5. 处理result字段
            String resultStr = detailResponse.getResult();
            if (resultStr == null || resultStr.isEmpty()) {
                log.warn("商品详情结果为空，SKU: {}", goodsSkuCode);
                return null;
            }

            // 6. 解析商品详情列表
            List<YZHGoodsSkuDTO> detailList;
            try {
                detailList = JSONArray.parseArray(resultStr, YZHGoodsSkuDTO.class);
            } catch (Exception e) {
                log.error("商品详情列表解析失败，SKU: {}, 结果: {}, 异常: {}", goodsSkuCode, resultStr, e.getMessage());
                return null;
            }

            if (detailList == null || detailList.isEmpty()) {
                log.warn("商品详情列表为空，SKU: {}", goodsSkuCode);
                return null;
            }

            // 7. 返回第一个商品详情（通常只有一个）
            YZHGoodsSkuDTO goodsDetail = detailList.get(0);
            log.info("获取商品详情成功，SKU: {}, 商品名称: {}, 价格: {}",
                    goodsSkuCode, goodsDetail.getGoodsName(), goodsDetail.getSellPrice());

            return goodsDetail;

        } catch (Exception e) {
            log.error("获取云中鹤商品详情时发生异常，SKU: {}, 异常: {}", goodsSkuCode, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解析价格字符串为BigDecimal
     */
    private BigDecimal parsePrice(String priceStr) {
        if (priceStr == null || priceStr.trim().isEmpty()) {
            return null;
        }
        try {
            String cleanPrice = priceStr.replaceAll("[^0-9.]", "");
            return new BigDecimal(cleanPrice);
        } catch (Exception e) {
            log.error("解析价格失败，价格: {}, 错误: {}", priceStr, e.getMessage());
            return null;
        }
    }

    /**
     * 更新商品价格
     */
    private void updateGoodsPrice(String goodsSkuCode, BigDecimal newPrice) {
        if (goodsSkuCode == null || goodsSkuCode.trim().isEmpty() ||
            newPrice == null || newPrice.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("参数无效，SKU: {}, 价格: {}", goodsSkuCode, newPrice);
            return;
        }

        try {
            // 计算相关价格
            BigDecimal originalPrice = newPrice;
            BigDecimal platformPrice = newPrice.multiply(new BigDecimal("1.10")).setScale(2, RoundingMode.HALF_UP);

            // 查询并更新商品信息
            GoodsSearchParams goodsSearchParams = new GoodsSearchParams();
            goodsSearchParams.setSupplierGoodsId(goodsSkuCode);
            Goods goods = goodsClient.getGoodsByParams(goodsSearchParams);

            if (goods == null) {
                log.warn("未找到商品记录，SKU: {}", goodsSkuCode);
                return;
            }

            // 更新商品价格
            goods.setPrice(newPrice.doubleValue());
            goods.setOriginalPrice(originalPrice.doubleValue());
            goods.setPlatformPrice(platformPrice.doubleValue());

            // 调用商品服务更新商品信息
            SupplierGoodsOperationDTO supplierGoodsOperationDTO = new SupplierGoodsOperationDTO();
            supplierGoodsOperationDTO.setGoodsId(goods.getId());
            supplierGoodsOperationDTO.setPrice(newPrice.doubleValue());
            supplierGoodsOperationDTO.setOriginalPrice(originalPrice.doubleValue());
            goodsClient.editSupplierGoods(supplierGoodsOperationDTO);

            // 更新商品SKU价格
            GoodsSearchParams skuSearchParams = new GoodsSearchParams();
            skuSearchParams.setGoodsId(goods.getId());
            List<GoodsSku> goodsSkuList = goodsClient.getGoodsSkuByList(skuSearchParams);

            if (goodsSkuList != null && !goodsSkuList.isEmpty()) {
                goodsSkuList.forEach(goodsSku -> {
                    goodsSku.setPrice(newPrice.doubleValue());
                    goodsSku.setOriginalPrice(originalPrice.doubleValue());
                    goodsClient.updateGoodsSku(goodsSku);
                });
            }

            log.info("商品价格更新完成，SKU: {}, 价格: {}", goodsSkuCode, newPrice);
        } catch (Exception e) {
            log.error("更新商品价格失败，SKU: {}, 价格: {}, 错误: {}", goodsSkuCode, newPrice, e.getMessage(), e);
        }
    }

    @Override
    public YzhDeleteMsgResponse deleteProcessedMessages(List<YzhQueryMsgListResponse.YzhMessage> messages) {
        if (messages == null || messages.isEmpty()) {
            log.warn("消息列表为空，无需删除");
            return YzhDeleteMsgResponse.success("无消息需要删除");
        }

        try {
            log.info("开始删除已处理的消息，消息数量: {}", messages.size());

            // 1. 获取AccessToken
            String accessToken = tpiHttpUtils.getToken();
            if (accessToken == null || accessToken.isEmpty()) {
                log.error("获取AccessToken失败，无法删除消息");
                return YzhDeleteMsgResponse.error("10001", "获取AccessToken失败");
            }

            // 2. 构建删除消息请求参数
            List<YzhDeleteMsgRequestParam.YzhDeleteMsgItem> deleteItems = messages.stream()
                    .map(message -> YzhDeleteMsgRequestParam.YzhDeleteMsgItem.builder()
                            .id(message.getId())
                            .messageType(message.getMessageType().toString())
                            .build())
                    .toList();

            YzhDeleteMsgRequestParam deleteRequest = YzhDeleteMsgRequestParam.builder()
                    .accessToken(accessToken)
                    .list(deleteItems)
                    .build();

            // 3. 调用云中鹤删除消息接口
            String deleteMsgUrl = tpiProperties.getDeleteMsgUrl();

            log.info("调用云中鹤删除消息接口，URL: {}, 请求参数: {}", deleteMsgUrl, JSONObject.toJSONString(deleteRequest));

            String responseStr = HttpUtils.doPostWithJson(deleteMsgUrl, deleteRequest);

            log.info("云中鹤删除消息接口响应: {}", responseStr);

            // 4. 解析响应结果
            if (responseStr == null || responseStr.isEmpty()) {
                log.error("删除消息接口响应为空");
                return YzhDeleteMsgResponse.error("10002", "删除消息接口响应为空");
            }

            YzhDeleteMsgResponse deleteResponse;
            try {
                deleteResponse = JSONObject.parseObject(responseStr, YzhDeleteMsgResponse.class);
            } catch (Exception e) {
                log.error("删除消息响应JSON解析失败，响应内容: {}, 异常: {}", responseStr, e.getMessage());
                return YzhDeleteMsgResponse.error("10003", "删除消息响应解析失败");
            }

            // 5. 验证删除结果
            if (deleteResponse == null) {
                log.error("删除消息响应解析为空");
                return YzhDeleteMsgResponse.error("10004", "删除消息响应解析为空");
            }

            if (!Boolean.TRUE.equals(deleteResponse.getSuccess()) || !"00000".equals(deleteResponse.getCode())) {
                log.error("删除消息失败，响应: {}", deleteResponse.getDesc());
                return YzhDeleteMsgResponse.error(deleteResponse.getCode(), deleteResponse.getDesc());
            }

            log.info("删除已处理消息成功，删除数量: {}", messages.size());
            return YzhDeleteMsgResponse.success("删除消息成功，删除数量: " + messages.size());

        } catch (Exception e) {
            log.error("删除已处理消息时发生异常，消息数量: {}, 异常: {}", messages.size(), e.getMessage(), e);
            return YzhDeleteMsgResponse.error("10005", "删除消息时发生异常: " + e.getMessage());
        }
    }

}