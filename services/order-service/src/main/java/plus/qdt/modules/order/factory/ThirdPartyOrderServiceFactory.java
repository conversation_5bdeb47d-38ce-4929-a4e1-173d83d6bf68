package plus.qdt.modules.order.factory;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import plus.qdt.modules.goods.entity.enums.SupplierEnum;
import plus.qdt.modules.order.service.AbstractThirdPartyOrderService;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 三方订单服务工厂
 * 使用工厂模式管理不同供应商的订单服务实例
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Slf4j
@Component
public class ThirdPartyOrderServiceFactory {

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 获取指定供应商的订单服务
     *
     * @param supplier 供应商枚举
     * @return 订单服务实例
     */
    public AbstractThirdPartyOrderService getService(SupplierEnum supplier) {
        if (supplier == null || SupplierEnum.CUSTOM.equals(supplier)) {
            return null; // 系统商品不需要三方订单服务
        }

        // 创建新实例
        AbstractThirdPartyOrderService service = createService(supplier);

        return service;
    }

    /**
     * 创建服务实例
     *
     * @param supplier 供应商枚举
     * @return 服务实例
     */
    private AbstractThirdPartyOrderService createService(SupplierEnum supplier) {
        try {
            // 根据供应商类型获取对应的服务实现类
            String serviceClassName = getServiceClassName(supplier);
            if (serviceClassName == null) {
                log.warn("未配置{}的订单服务实现类", supplier.getDescription());
                return null;
            }

            Class<?> serviceClass = Class.forName(serviceClassName);
            
            // 从Spring容器中获取Bean
            Object bean = applicationContext.getBean(serviceClass);
            
            if (bean instanceof AbstractThirdPartyOrderService) {
                return (AbstractThirdPartyOrderService) bean;
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("创建{}订单服务实例失败: {}", supplier.getDescription(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据供应商类型获取服务实现类名
     *
     * @param supplier 供应商类型
     * @return 服务实现类名
     */
    private String getServiceClassName(SupplierEnum supplier) {
        return switch (supplier) {
            case YZH -> "plus.qdt.modules.order.order.serviceimpl.YzhThirdPartyOrderServiceImpl";
            default -> null;
        };
    }
}
