package plus.qdt.modules.order.order.serviceimpl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import plus.qdt.common.enums.ClientTypeEnum;
import plus.qdt.common.enums.PromotionTypeEnum;
import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.event.TransactionCommitSendMQEvent;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.security.AuthUser;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.security.enums.SceneEnums;
import plus.qdt.common.utils.CurrencyUtil;
import plus.qdt.common.utils.GsonUtils;
import plus.qdt.common.utils.SnowFlake;
import plus.qdt.common.utils.StringUtils;
import plus.qdt.exchange.AmqpExchangeProperties;
import plus.qdt.logs.annotation.SystemLogPoint;
import plus.qdt.modules.domain.dto.PayOrderTopDto;
import plus.qdt.modules.domain.vo.OrderTopVo;
import plus.qdt.modules.goods.client.GoodsClient;
import plus.qdt.modules.goods.entity.dto.GoodsCompleteMessage;
import plus.qdt.modules.goods.entity.dto.GoodsTopDto;
import plus.qdt.modules.goods.entity.enums.GoodsType;
import plus.qdt.modules.jinTongAccount.client.AccountConsumeClient;
import plus.qdt.modules.member.entity.dto.MemberAddressDTO;
import plus.qdt.modules.message.client.MessageClient;
import plus.qdt.modules.message.entity.dos.Message;
import plus.qdt.modules.message.entity.enums.MessageRangeEnum;
import plus.qdt.modules.order.cart.entity.dto.TradeDTO;
import plus.qdt.modules.order.cart.entity.enums.DeliveryMethodEnum;
import plus.qdt.modules.order.cart.entity.vo.CartSkuVO;
import plus.qdt.modules.order.cart.entity.vo.CartVO;
import plus.qdt.modules.order.order.aop.OrderLogPoint;
import plus.qdt.modules.order.order.entity.dos.*;
import plus.qdt.modules.order.order.entity.dto.*;
import plus.qdt.modules.order.order.entity.enums.*;
import plus.qdt.modules.order.order.entity.vo.OrderDetailVO;
import plus.qdt.modules.order.order.entity.vo.OrderSimpleVO;
import plus.qdt.modules.order.order.entity.vo.OrderStatusCountVO;
import plus.qdt.modules.order.order.entity.vo.OrderVO;
import plus.qdt.modules.order.order.mapper.OrderMapper;
import plus.qdt.modules.order.order.service.*;
import plus.qdt.modules.order.order.serviceimpl.YzhThirdPartyOrderServiceImpl;
import plus.qdt.modules.order.order.manager.ThirdPartyOrderCancelManager;
import plus.qdt.modules.order.order.entity.dto.ThirdPartyCancelResult;
import plus.qdt.modules.order.logistics.strategy.LogisticsQueryStrategy;
import plus.qdt.modules.order.logistics.factory.LogisticsQueryStrategyFactory;
import plus.qdt.modules.order.trade.entity.dos.OrderLog;
import plus.qdt.modules.payment.entity.dos.PaymentLog;
import plus.qdt.modules.payment.entity.enums.PaymentMethodEnum;
import plus.qdt.modules.promotion.client.PromotionsClient;
import plus.qdt.modules.promotion.entity.dos.Pintuan;
import plus.qdt.modules.system.client.LogisticsClient;
import plus.qdt.modules.system.client.RegionClient;
import plus.qdt.modules.system.entity.dos.Logistics;
import plus.qdt.modules.system.entity.dos.Region;
import plus.qdt.modules.system.entity.enums.MessageType;
import plus.qdt.modules.system.entity.vo.Traces;
import plus.qdt.mybatis.util.PageUtil;
import plus.qdt.mybatis.util.SceneHelp;
import plus.qdt.routing.GoodsRoutingKey;
import plus.qdt.routing.OrderRoutingKey;
import plus.qdt.routing.PromotionRoutingKey;
import plus.qdt.trigger.enums.DelayTypeEnums;
import plus.qdt.trigger.interfaces.TimeTrigger;
import plus.qdt.trigger.message.PintuanOrderMessage;
import plus.qdt.trigger.model.TimeExecuteConstant;
import plus.qdt.trigger.model.TimeTriggerMsg;
import plus.qdt.trigger.util.DelayQueueTools;
import plus.qdt.util.AmqpMessage;
import plus.qdt.util.AmqpSender;

import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 子订单业务层实现
 *
 * <AUTHOR>
 * @since 2020/11/17 7:38 下午
 */
@Slf4j
@Service
@GlobalTransactional
@RequiredArgsConstructor
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements OrderService {

    private static final String ORDER_SN_COLUMN = "order_sn";
    /**
     * 延时任务
     */
    private final TimeTrigger timeTrigger;
    /**
     * 订单货物数据层
     */
    private final OrderItemService orderItemService;
    /**
     * 发票
     */
    private final ReceiptService receiptService;
    /**
     * 物流公司
     */
    private final LogisticsClient logisticsClient;
    /**
     * 订单日志
     */
    private final OrderLogService orderLogService;
    /**
     * 订单流水
     */
    private final OrderFlowService orderFlowService;
    /**
     * 售后流水
     */
    private final RefundFlowService refundFlowService;
    /**
     * 拼团
     */
    private final PromotionsClient promotionsClient;

    private final AmqpSender amqpSender;


    private final ApplicationEventPublisher applicationEventPublisher;

    private final AmqpExchangeProperties amqpExchangeProperties;

    private final OrderPackageService orderPackageService;

    private final OrderPackageItemService orderPackageItemService;

    private final GoodsClient goodsClient;
    private final QdtDigitalAgencyService digitalAgencyService;
    private final RegionClient regionClient;
    private final AccountConsumeClient accountConsumeClient;
    private final MessageClient messageClient;
    private final YzhThirdPartyOrderServiceImpl yzhThirdPartyOrderService;
    private final ThirdPartyOrderCancelManager thirdPartyOrderCancelManager;
    private final LogisticsQueryStrategyFactory logisticsQueryStrategyFactory;

    @Override
    @Transactional
    public void intoDB(TradeDTO tradeDTO) {
        //检查TradeDTO信息
        checkTradeDTO(tradeDTO);
        //存放购物车，即业务中的订单
        List<Order> orders = new ArrayList<>(tradeDTO.getCartList().size());
        //存放自订单/订单日志
        List<OrderItem> orderItems = new ArrayList<>();
        List<OrderLog> orderLogs = new ArrayList<>();

        //订单集合
        List<OrderVO> orderVOS = new ArrayList<>();
        //循环购物车
        for (CartVO item : tradeDTO.getCartList()) {
            if (item.getSkuList().stream().noneMatch(CartSkuVO::getChecked)) {
                continue;
            }
            Order order = new Order(item, tradeDTO);
            //构建orderVO对象
            OrderVO orderVO = new OrderVO();
            BeanUtil.copyProperties(order, orderVO);
            //持久化DO
            orders.add(order);
            String message = "订单[" + item.getSn() + "]创建";
            //记录日志
            orderLogs.add(new OrderLog(item.getSn(), UserContext.getCurrentExistUser().getExtendId(),
                    UserContext.getCurrentExistUser().getScene().value(),
                    UserContext.getCurrentExistUser().getNickName(), message));
            for (CartSkuVO sku : item.getCheckedSkuList()) {
                OrderItem orderItem = new OrderItem(sku, item, tradeDTO);
                // 如果商品是数商大礼包，则需要判断是否有代理地址
                if (GoodsType.isDigitalAgency(orderItem.getCategoryId())) {
                    if (StringUtils.isBlank(tradeDTO.getAreaCode())) {
                        throw new ServiceException("数商礼包必须要有代理区域");
                    }
                    GoodsType goodsType = GoodsType.digitalAgency(orderItem.getCategoryId());
                    Region region = regionClient.getRegionById(tradeDTO.getAreaCode());
                    if (!goodsType.getRegionLevel().equals(region.getLevel())) {
                        throw new ServiceException(goodsType.getDescription() + "行政区划代码级别错误，请检查");
                    }
                    if (digitalAgencyService.isProxy(tradeDTO.getAreaCode())) {
                        throw new ServiceException("该区域已被代理");
                    }
                    // 保存数商代理
                    digitalAgencyService.save(new QdtDigitalAgency()
                            .setOrderId(item.getSn())
                            .setProxyCode(tradeDTO.getAreaCode())
                            .setProxy(false) // 未支付，未代理
                            .setUserId(UserContext.getCurrentExistUser().getId())
                            .setType(goodsType.name()));
                }
                orderItems.add(orderItem);
            }
            //写入子订单信息
            orderVO.setOrderItems(orderItems);
            //orderVO 记录
            orderVOS.add(orderVO);
        }
        tradeDTO.setOrderVO(orderVOS);
        //批量保存订单
        super.saveBatch(orders);
        //批量保存 子订单
        orderItemService.saveBatch(orderItems);
        //批量记录订单操作日志
        orderLogService.saveBatch(orderLogs);
    }

    @Override
    @Transactional
    public Order payOrder(PaymentLog paymentLog) {
        //获取订单
        Order order = this.getBySn(paymentLog.getOrderSn());


        //修改订单状态
        order.setPaymentTime(new Date());
        order.setPaymentMethod(paymentLog.getPaymentMethod());
        order.setPayStatus(PayStatusEnum.PAID.name());
        order.setOrderStatus(OrderStatusEnum.PAID.name());
        order.setOutTradeNo(paymentLog.getOutTradeNo());
        order.setTransactionId(paymentLog.getTransactionId());
        order.setCanReturn(PaymentMethodEnum.canReturnOnline(paymentLog.getPaymentMethod()));
        this.updateById(order);

        //发送订单已付款消息
        OrderMessage orderMessage = new OrderMessage();
        orderMessage.setOrderSn(order.getSn());
        orderMessage.setPaymentMethod(order.getPaymentMethod());
        orderMessage.setNewStatus(OrderStatusEnum.PAID);
        this.sendUpdateStatusMessage(orderMessage);

        String message = "订单付款，付款方式[" + PaymentMethodEnum.valueOf(order.getPaymentMethod())
                .paymentName() + "]";


        orderFlowService.payOrder(order, orderItemService.getByOrderSn(order.getSn()));

        String orderLogName = order.getNickname();
        OrderLog orderLog = new OrderLog(order.getSn(), order.getBuyerId(), SceneEnums.MEMBER.name(), orderLogName, message);
        orderLogService.save(orderLog);

        // 处理三方订单创建
        try {
            processThirdPartyOrderCreation(order);
        } catch (Exception e) {
            log.error("处理三方订单创建失败，订单号: {}, 异常: {}", order.getSn(), e.getMessage());
            // 三方订单创建失败不影响主流程，只记录日志
        }
        return order;
    }

    @Override
    @Transactional
    public void payOrderZero(String sn) {

        //订单列表
        List<Order> orders = this.getByTradeSn(sn);

        for (Order order : orders) {
            //修改订单状态
            order.setPaymentTime(new Date());
            order.setPaymentMethod(PaymentMethodEnum.BANK_TRANSFER.name());
            order.setPayStatus(PayStatusEnum.PAID.name());
            order.setOrderStatus(OrderStatusEnum.PAID.name());
            order.setOutTradeNo("-1");
            order.setTransactionId("-1");
            order.setCanReturn(false);
            this.updateById(order);

            //发送订单已付款消息
            OrderMessage orderMessage = new OrderMessage();
            orderMessage.setOrderSn(order.getSn());
            orderMessage.setPaymentMethod(order.getPaymentMethod());
            orderMessage.setNewStatus(OrderStatusEnum.PAID);
            this.sendUpdateStatusMessage(orderMessage);

            String message = "订单金额为0元，自动修改订单状态";


            orderFlowService.payOrder(order, orderItemService.getByOrderSn(order.getSn()));

            OrderLog orderLog = new OrderLog(order.getSn(), "-1", SceneEnums.SYSTEM.name(), "系统操作", message);
            orderLogService.save(orderLog);
        }

    }

    @Override
    public void payOrderZeroByOrderSn(String oderSn) {
        Order order = this.getBySn(oderSn);
        if (order == null) {
            throw new ServiceException(ResultCode.ORDER_NOT_EXIST);
        }
        //修改订单状态
        order.setPaymentTime(new Date());
        order.setPaymentMethod(PaymentMethodEnum.BANK_TRANSFER.name());
        order.setPayStatus(PayStatusEnum.PAID.name());
        order.setOrderStatus(OrderStatusEnum.PAID.name());
        order.setOutTradeNo("-1");
        order.setTransactionId("-1");
        order.setCanReturn(false);
        this.updateById(order);

        //发送订单已付款消息
        OrderMessage orderMessage = new OrderMessage();
        orderMessage.setOrderSn(order.getSn());
        orderMessage.setPaymentMethod(order.getPaymentMethod());
        orderMessage.setNewStatus(OrderStatusEnum.PAID);
        this.sendUpdateStatusMessage(orderMessage);

        String message = "订单金额为0元，自动修改订单状态";

        orderFlowService.payOrder(order, orderItemService.getByOrderSn(order.getSn()));

        OrderLog orderLog = new OrderLog(order.getSn(), "-1", SceneEnums.SYSTEM.name(), "系统操作", message);
        orderLogService.save(orderLog);
    }

    @Override
    public Page<OrderSimpleVO> queryByParams(OrderSearchParams orderSearchParams) {
        QueryWrapper<OrderSimpleVO> queryWrapper = orderSearchParams.queryWrapper();
        queryWrapper.groupBy("o.id");
        queryWrapper.orderByDesc("o.id");
        return this.baseMapper.queryByParams(PageUtil.initPage(orderSearchParams), queryWrapper);
    }

    /**
     * 订单信息
     *
     * @param orderSearchParams 查询参数
     * @return 订单信息
     */
    @Override
    public List<Order> queryListByParams(OrderSearchParams orderSearchParams) {
        return this.baseMapper.queryListByParams(orderSearchParams.queryWrapper());
    }

    /**
     * 根据促销查询订单
     *
     * @param orderPromotionType 订单类型
     * @param payStatus          支付状态
     * @param parentOrderSn      依赖订单编号
     * @param orderSn            订单编号
     * @return 订单信息
     */
    @Override
    public List<Order> queryListByPromotion(String orderPromotionType, String payStatus, String parentOrderSn, String orderSn) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        //查找团长订单和已和当前拼团订单拼团的订单
        queryWrapper.eq(Order::getOrderPromotionType, orderPromotionType)
                .eq(Order::getPayStatus, payStatus)
                .and(i -> i.eq(Order::getParentOrderSn, parentOrderSn)
                        .or(j -> j.eq(Order::getSn, orderSn)));
        return this.list(queryWrapper);
    }

    @Override
    public List<Order> queryListByPromotionId(String orderPromotionType, String payStatus, String parentOrderSn, String promotionId, String skuId) {
        //查找待拼团的订单
        return this.baseMapper.queryListByPromotionId(orderPromotionType, payStatus, parentOrderSn, promotionId, skuId);
    }

    /**
     * 根据促销查询订单
     *
     * @param orderPromotionType 订单类型
     * @param payStatus          支付状态
     * @param parentOrderSn      依赖订单编号
     * @param orderSn            订单编号
     * @return 订单信息
     */
    @Override
    public long queryCountByPromotion(String orderPromotionType, String payStatus, String parentOrderSn, String orderSn) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        //查找团长订单和已和当前拼团订单拼团的订单
        queryWrapper.eq(Order::getOrderPromotionType, orderPromotionType).eq(Order::getPayStatus, payStatus).and(i -> i.eq(Order::getParentOrderSn,
                parentOrderSn).or(j -> j.eq(Order::getSn, orderSn)));
        return this.count(queryWrapper);
    }

    /**
     * 获取未成团拼团订单
     *
     * @param pintuanId 拼团id
     * @return 拼团订单信息
     */
    @Override
    public List<Order> queryListByPromotion(String pintuanId) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getOrderPromotionType, PromotionTypeEnum.PINTUAN.name());
        queryWrapper.eq(Order::getPromotionId, pintuanId);
        queryWrapper.in(Order::getOrderStatus, OrderStatusEnum.UNPAID.name(), OrderStatusEnum.PAID.name());
        queryWrapper.ne(Order::getOrderStatus, OrderStatusEnum.CANCELLED.name());
        return this.list(queryWrapper);
    }

    @Override
    public void queryExportOrder(HttpServletResponse response, OrderSearchParams orderSearchParams) {

        XSSFWorkbook workbook = initOrderExportData(this.baseMapper.queryExportOrder(orderSearchParams.queryWrapper()));
        try {
            // 设置响应头
            String fileName = URLEncoder.encode("订单列表", StandardCharsets.UTF_8);
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

            ServletOutputStream out = response.getOutputStream();
            workbook.write(out);
        } catch (Exception e) {
            log.error("导出订单失败", e);
        } finally {
            try {
                workbook.close();
            } catch (Exception e) {
                log.error("关闭流异常", e);
            }
        }
    }

    @Override
    public OrderDetailVO queryDetail(String orderSn) {
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sn", orderSn);

        AuthUser currentUser = UserContext.getCurrentUser();
        if (currentUser != null) {
            switch (currentUser.getScene()) {
                case MEMBER -> queryWrapper.eq("buyer_id", currentUser.getExtendId());
                case STORE -> queryWrapper.eq("store_id", currentUser.getExtendId()).or().eq("buyer_id",
                        currentUser.getExtendId());
                case SUPPLIER -> queryWrapper.eq("store_id", currentUser.getExtendId()).or().eq("supplier_id",
                        currentUser.getExtendId());
                default -> {
                }
            }
        }


        Order order = this.getBySn(orderSn);
        if (order == null) {
            throw new ServiceException(ResultCode.ORDER_NOT_EXIST);
        }
        QueryWrapper<OrderItem> orderItemWrapper = new QueryWrapper<>();
        orderItemWrapper.eq(ORDER_SN_COLUMN, orderSn);
        //查询订单项信息
        List<OrderItem> orderItems = orderItemService.list(orderItemWrapper);
        //查询订单日志信息
        List<OrderLog> orderLogs = orderLogService.getOrderLog(orderSn);
        //查询发票信息
        Receipt receipt = receiptService.getByOrderSn(orderSn);
        //查询订单和自订单，然后写入vo返回
        return new OrderDetailVO(order, orderItems, orderLogs, receipt);
    }

    @Override
    @OrderLogPoint(description = "'订单['+#orderSn+']取消，原因为：'+#reason", orderSn = "#orderSn")
    @Transactional
    public List<Order> cancel(String tradeSn, String reason) {
        List<Order> orderList = this.getByTradeSn(tradeSn);
        for (Order order : orderList) {
            SceneHelp.objectAuthentication(order);

            //如果订单促销类型不为空&&订单是拼团订单，并且订单未成团，则抛出异常
            if (OrderPromotionTypeEnum.PINTUAN.name().equals(order.getOrderPromotionType()) &&
                    !CharSequenceUtil.equalsAny(order.getOrderStatus(), OrderStatusEnum.TAKE.name(), OrderStatusEnum.UNDELIVERED.name(),
                            OrderStatusEnum.STAY_PICKED_UP.name())) {
                throw new ServiceException(ResultCode.ORDER_PINTUAN_CANNOT_CANCEL);
            }

            switch (OrderStatusEnum.valueOf(order.getOrderStatus())) {
                case UNDELIVERED:
                case UNPAID:
                case PAID:
                    // 以上状态可正常进行订单取消流程
                    this.cancelOrder(order.getSn(), reason);
                    order.setOrderStatus(OrderStatusEnum.CANCELLED.name());
                    order.setCancelReason(reason);
                    //退款
                    refundFlowService.generatorRefundFlow(order);
                    orderStatusMessage(order);
                    break;
                case CANCELLED:
                    // 这里兼容部分特殊情况，例如赠品订单无库存时为取消状态，这里直接跳过。
                    break;
                default:
                    // 其他状态直接抛出异常，不可取消订单
                    throw new ServiceException(ResultCode.ORDER_CAN_NOT_CANCEL);
            }
        }

        return orderList;
    }

    @Override
    @OrderLogPoint(description = "'订单['+#orderSn+']系统取消，原因为：'+#reason", orderSn = "#orderSn")
    @Transactional
    public void systemCancel(String orderSn, String reason, Boolean refundMoney) {
        Order order = this.getBySn(orderSn);
        order.setOrderStatus(OrderStatusEnum.CANCELLED.name());
        order.setCancelReason(reason);
        this.cancelOrder(orderSn, reason);
        //生成店铺退款流水
        if (Boolean.TRUE.equals(refundMoney) && PayStatusEnum.PAID.name().equals(order.getPayStatus())) {
            refundFlowService.generatorRefundFlow(order);
            orderStatusMessage(order);
        }else{
            orderFlowService.updateOrderFlowStatus(orderSn,ProfitSharingStatusEnum.ORDER_CANCEL.name());
        }

        // 处理三方订单取消逻辑
        try {
            processThirdPartyOrderCancel(order);
        } catch (Exception e) {
            log.error("处理三方订单取消失败，订单号: {}, 异常: {}", orderSn, e.getMessage(), e);
            // 三方订单取消失败不影响主流程，只记录日志
        }
    }

    /**
     * 获取订单
     *
     * @param orderSn 订单编号
     * @return 订单详情
     */
    @Override
    public Order getBySn(String orderSn) {

        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sn", orderSn);
        if (UserContext.getCurrentUser() != null) {
            switch (Objects.requireNonNull(UserContext.getCurrentUser()).getScene()) {
                case MEMBER -> queryWrapper.eq("buyer_id", UserContext.getCurrentUser().getExtendId());
                case SUPPLIER ->
                        queryWrapper.nested(n -> n.eq("supplier_id", UserContext.getCurrentUser().getExtendId()).or().eq("store_id",
                                UserContext.getCurrentUser().getExtendId()));
                case STORE ->
                        queryWrapper.nested(n -> n.eq("store_id", UserContext.getCurrentUser().getExtendId()).or().eq("buyer_id",
                                UserContext.getCurrentUser().getExtendId()));
                default -> {
                }
            }
        }

        Order order = this.getOne(queryWrapper, false);
        if (order == null) {
            throw new ServiceException(ResultCode.ORDER_NOT_EXIST);
        }
        return order;
    }

    @Override
    public Order checkDeliver(String orderSn) {
        Order order = this.getBySn(orderSn);
        //判断订单状促销状态不为空，且是否为赠品订单
        if (CharSequenceUtil.isNotEmpty(order.getOrderPromotionType()) && order.getOrderPromotionType().equals(OrderPromotionTypeEnum.GIFT.name())) {

            List<Order> orders = this.getByTradeSn(order.getTradeSn());
            orders.forEach(o -> {
                //判定当前店铺订单
                if (o.getStoreId().equals(order.getStoreId())) {
                    //判断订单状态是否为待发货&&订单促销类型不为空&&订单促销类型不为赠品
                    if (o.getDeliverStatus().equals(DeliverStatusEnum.UNDELIVERED.name()) &&
                            CharSequenceUtil.isNotEmpty(order.getOrderPromotionType()) &&
                            !o.getOrderPromotionType().equals(OrderPromotionTypeEnum.GIFT.name())) {
                        throw new ServiceException(ResultCode.ORDER_GIFT_DELIVER_ERROR);
                    }
                }
            });

        }
        return order;
    }

    @Override
    public Order getBySnNoAuth(String orderSn) {
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sn", orderSn);
        Order order = this.getOne(queryWrapper);
        if (order == null) {
            throw new ServiceException(ResultCode.ORDER_NOT_EXIST);
        }
        return order;
    }

    @Override
    @OrderLogPoint(description = "'库存确认'", orderSn = "#orderSn")
    @Transactional
    public void afterOrderConfirm(String orderSn) {
        Order order = this.getBySn(orderSn);
        //判断是否为拼团订单，进行特殊处理
        //判断订单类型进行不同的订单确认操作
        if (OrderPromotionTypeEnum.PINTUAN.name().equals(order.getOrderPromotionType())) {
            String parentOrderSn = CharSequenceUtil.isEmpty(order.getParentOrderSn()) ? orderSn : order.getParentOrderSn();
            this.checkPintuanOrder(order.getPromotionId(), parentOrderSn);
        } else {
            //判断订单类型
            if (order.getOrderType().equals(OrderTypeEnum.NORMAL.name()) || order.getOrderType().equals(OrderTypeEnum.PURCHASE.name())) {
                normalOrderConfirm(orderSn);
            } else {
                virtualOrderConfirm(orderSn);
            }
        }
    }


    @Override
    @SystemLogPoint(description = "修改订单", customerLog = "'订单[' + #orderSn + ']收货信息修改，修改为'+#memberAddressDTO.fullAddress+'")
    @Transactional
    public Order updateConsignee(String orderSn, MemberAddressDTO memberAddressDTO) {
        Order order = this.getBySn(orderSn);
        //要记录之前的收货地址，所以需要以代码方式进行调用 不采用注解
        String message = "订单[" + orderSn + "]收货信息修改，由[" + order.getFullAddress() + "]修改为[" + memberAddressDTO.getFullAddress() + "]";
        //记录订单操作日志
        BeanUtil.copyProperties(memberAddressDTO, order);
        this.updateById(order);
        AuthUser authUser = UserContext.getCurrentExistUser();
        OrderLog orderLog = new OrderLog(orderSn, authUser.getId(), authUser.getScene().value(),
                authUser.getScene().equals(SceneEnums.MEMBER) ? authUser.getNickName() : authUser.getExtendName(), message);
        orderLogService.save(orderLog);

        return order;
    }

    @Override
    public Order updateRemark(String orderSn, String remark) {
        Order order = this.getBySn(orderSn);
        UpdateWrapper<Order> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("sn", orderSn);
        updateWrapper.set("remark", remark);
        this.update(updateWrapper);
        return order;
    }

    @Override
    public Order updateSellerRemark(String orderSn, String sellerRemark) {
        Order order = this.getBySn(orderSn);
        order.setSellerRemark(sellerRemark);
        this.updateById(order);
        return order;
    }

    @Override
    @OrderLogPoint(description = "'订单['+#orderSn+']发货，发货单号['+#logisticsNo+']'", orderSn = "#orderSn")
    @Transactional
    public Order delivery(String orderSn, String logisticsNo, String logisticsId) {
        Order order = this.checkDeliver(orderSn);

        //校验订单是否可以发货
        List<OrderFlow> orderFlowList = orderFlowService.list(new QueryWrapper<OrderFlow>().eq("order_sn", order.getSn()));
        if (orderFlowList != null) {
            orderFlowList.forEach(item -> {
                if (Boolean.FALSE.equals(item.verify())) {
                    throw new ServiceException(ResultCode.ORDER_VERIFY_ERROR, item.verifyMessage());
                }
            });
        }


        //如果订单未发货，并且订单状态值等于待发货
        if (order.getDeliverStatus().equals(DeliverStatusEnum.UNDELIVERED.name()) && (order.getOrderStatus().equals(OrderStatusEnum.UNDELIVERED.name()) || order.getOrderStatus().equals(OrderStatusEnum.PARTS_DELIVERED.name()))) {
            //获取对应物流
            Logistics logistics = logisticsClient.getById(logisticsId);
            if (logistics == null) {
                throw new ServiceException(ResultCode.ORDER_LOGISTICS_ERROR);
            }
            //写入物流信息
            order.setLogisticsCode(logistics.getId());
            order.setLogisticsName(logistics.getName());
            order.setLogisticsNo(logisticsNo);
            order.setLogisticsTime(new Date());
            order.setDeliverStatus(DeliverStatusEnum.DELIVERED.name());
            this.updateById(order);
            //修改订单状态为已发送
            this.updateStatus(orderSn);
            //修改订单货物可以进行售后、投诉
            orderItemService.update(new UpdateWrapper<OrderItem>().eq(ORDER_SN_COLUMN, orderSn).eq("after_sale_status", OrderItemAfterSaleStatusEnum.NEW)
                    .set("after_sale_status", OrderItemAfterSaleStatusEnum.NOT_APPLIED)
                    .set("complain_status", OrderComplaintStatusEnum.NO_APPLY));
            //发送订单状态改变消息
            OrderMessage orderMessage = new OrderMessage();
            orderMessage.setNewStatus(OrderStatusEnum.DELIVERED);
            orderMessage.setOrderSn(order.getSn());
            this.sendUpdateStatusMessage(orderMessage);
        } else {
            throw new ServiceException(ResultCode.ORDER_DELIVER_ERROR);
        }
        return order;
    }

    @Override
    public Traces getTraces(String orderSn) {
        // 获取订单信息
        Order order = this.getBySn(orderSn);

        // 获取订单项列表
        List<OrderItem> orderItems = orderItemService.getByOrderSn(orderSn);

        // 使用策略模式获取适合的物流查询策略
        LogisticsQueryStrategy strategy = logisticsQueryStrategyFactory.getStrategy(order, orderItems);

        if (strategy == null) {
            throw new ServiceException("未找到适合的物流查询策略");
        }

        // 执行物流查询
        return strategy.queryLogistics(order, orderItems);
    }

    @Override
    @OrderLogPoint(description = "'订单['+#orderSn+']核销，核销码['+#verificationCode+']'", orderSn = "#orderSn")
    @Transactional
    public Order take(String orderSn, String verificationCode) {

        //获取订单信息
        Order order = this.getBySn(orderSn);
        //检测虚拟订单信息
        checkVerificationOrder(order, verificationCode);
        order.setOrderStatus(OrderStatusEnum.COMPLETED.name());
        //订单完成
        this.complete(orderSn);
        return order;
    }

    @Override
    public Order getOrderByVerificationCode(String verificationCode) {
        String storeId = Objects.requireNonNull(UserContext.getCurrentUser()).getExtendId();
        Order order = this.getOne(new LambdaQueryWrapper<Order>().eq(Order::getOrderStatus, OrderStatusEnum.TAKE.name()).eq(Order::getStoreId,
                storeId).eq(Order::getVerificationCode, verificationCode));

        if (order == null) {
            throw new ServiceException(ResultCode.ORDER_TAKE_ERROR);
        }
        return order;
    }

    @Override
    @OrderLogPoint(description = "'订单['+#orderSn+']完成'", orderSn = "#orderSn")
    @Transactional
    public void complete(String orderSn) {
        //是否可以查询到订单
        Order order = this.getBySn(orderSn);
        complete(order, orderSn);
    }

    @Override
    @OrderLogPoint(description = "'订单['+#orderSn+']完成'", orderSn = "#orderSn")
    @Transactional
    public void systemComplete(String orderSn) {
        Order order = this.getBySn(orderSn);
        complete(order, orderSn);
    }

    /**
     * 完成订单方法封装
     *
     * @param order   订单
     * @param orderSn 订单编号
     */
    @Transactional(rollbackFor = Exception.class)
    public void complete(Order order, String orderSn) {//修改订单状态为完成
//        this.updateStatus(orderSn, OrderStatusEnum.COMPLETED);

        //修改订单货物可以进行评价
        orderItemService.update(new UpdateWrapper<OrderItem>().eq(ORDER_SN_COLUMN, orderSn).set("comment_status", CommentStatusEnum.UNFINISHED));
        order.setOrderStatus(OrderStatusEnum.COMPLETED.name());
        order.setCompleteTime(new Date());
        this.updateById(order);

        //发送当前商品购买完成的信息（用于更新商品数据）
        List<OrderItem> orderItems = orderItemService.getByOrderSn(orderSn);
        List<GoodsCompleteMessage> goodsCompleteMessageList = new ArrayList<>();
        for (OrderItem orderItem : orderItems) {
            GoodsCompleteMessage goodsCompleteMessage = new GoodsCompleteMessage();
            goodsCompleteMessage.setGoodsId(orderItem.getGoodsId());
            goodsCompleteMessage.setSkuId(orderItem.getSkuId());
            goodsCompleteMessage.setBuyNum(orderItem.getNum());
            goodsCompleteMessage.setMemberId(order.getBuyerId());
            goodsCompleteMessageList.add(goodsCompleteMessage);
        }
        //发送商品购买消息
        if (!goodsCompleteMessageList.isEmpty()) {
            //发送订单变更mq消息
            amqpSender.send(AmqpMessage.builder().exchange(amqpExchangeProperties.getGoods()).routingKey(GoodsRoutingKey.BUY_GOODS_COMPLETE).message(goodsCompleteMessageList).build());
        }

        orderStatusMessage(order);
        // 发送订单状态完成MQ消息
        Message message = new Message();
        message.setTitle(MessageType.ORDER_COMPLETED.getTitle());
        message.setDataIds(List.of(order.getId()));
        message.setMessageRange(MessageRangeEnum.USER.name());
        // 购买商品
        Map<String, Object> content = new HashMap<>();
        content.put("images", orderItems.stream().map(OrderItem::getImage).toList());
        content.put("path", MessageType.ORDER_COMPLETED.getPath());
        message.setContent(JSONUtil.toJsonStr(content));
        // 订单完成
        message.setType(MessageType.ORDER_COMPLETED.name());
        message.setUserIds(new String[]{order.getBuyerId()});
        messageClient.sendMessage(message);
        // TODO 财通托管释放->分账
        accountConsumeClient.releaseMoney(orderSn);
    }

    @Override
    public List<Order> getByTradeSn(String tradeSn) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        return this.list(queryWrapper.eq(Order::getTradeSn, tradeSn));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendUpdateStatusMessage(OrderMessage orderMessage) {
        applicationEventPublisher.publishEvent(
                TransactionCommitSendMQEvent.builder()
                        .source("订单状态变更")
                        .exchange(amqpExchangeProperties.getOrder())
                        .routingKey(OrderRoutingKey.STATUS_CHANGE)
                        .message(orderMessage)
                        .build());
    }

    @Override
    @Transactional
    public void deleteOrder(String sn) {
        Order order = this.getBySn(sn);
        if (order == null) {
            log.error("订单号为" + sn + "的订单不存在！");
            throw new ServiceException();
        }
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getSn, sn).set(Order::getDeleteFlag, true);
        this.update(updateWrapper);
        LambdaUpdateWrapper<OrderItem> orderItemLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        orderItemLambdaUpdateWrapper.eq(OrderItem::getOrderSn, sn).set(OrderItem::getDeleteFlag, true);
        this.orderItemService.update(orderItemLambdaUpdateWrapper);
    }

    @Override
    public Boolean invoice(String sn) {
        //根据订单号查询发票信息
        Receipt receipt = receiptService.getByOrderSn(sn);
        //校验发票信息是否存在
        if (receipt != null) {
            receipt.setReceiptStatus(1);
            return receiptService.updateById(receipt);
        }
        throw new ServiceException(ResultCode.USER_RECEIPT_NOT_EXIST);
    }

    /**
     * 自动成团订单处理
     *
     * @param pintuanId     拼团活动id
     * @param parentOrderSn 拼团订单sn
     */
    @Override
    @Transactional
    public void agglomeratePintuanOrder(String pintuanId, String parentOrderSn) {
        //获取拼团配置
        Pintuan pintuan = promotionsClient.getPintuanVO(pintuanId);
        List<Order> list = this.getPintuanOrder(pintuanId, parentOrderSn);
        if (Boolean.TRUE.equals(pintuan.getFictitious()) && pintuan.getRequiredNum() > list.size()) {
            //如果开启虚拟成团且当前订单数量不足成团数量，则认为拼团成功
            this.pintuanOrderSuccess(list);
        } else if (Boolean.FALSE.equals(pintuan.getFictitious()) && pintuan.getRequiredNum() > list.size()) {
            //如果未开启虚拟成团且当前订单数量不足成团数量，则认为拼团失败
            this.pintuanOrderFailed(parentOrderSn);
        }
    }

    @Override
    public void getBatchDeliverList(HttpServletResponse response, List<String> logisticsName) {
        ExcelWriter writer = ExcelUtil.getWriter();
        //Excel 头部
        ArrayList<String> rows = new ArrayList<>();
        rows.add("订单编号");
        rows.add("物流公司");
        rows.add("物流编号");
        writer.writeHeadRow(rows);

        //存放下拉列表  ----店铺已选择物流公司列表
        String[] logiList = logisticsName.toArray(new String[]{});
        CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(1, 200, 1, 1);
        writer.addSelect(cellRangeAddressList, logiList);

        ServletOutputStream out = null;
        try {
            //设置公共属性，列表名称
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("批量发货导入模板", StandardCharsets.UTF_8) + ".xls");
            out = response.getOutputStream();
            writer.flush(out, true);
        } catch (Exception e) {
            log.error("获取待发货订单编号列表错误", e);
        } finally {
            writer.close();
            IoUtil.close(out);
        }

    }

    @Override
    @Transactional
    public void batchDeliver(MultipartFile files) {

        InputStream inputStream;
        List<OrderBatchDeliverDTO> orderBatchDeliverDTOList = new ArrayList<>();
        try {
            inputStream = files.getInputStream();
            //2.应用HUtool ExcelUtil获取ExcelReader指定输入流和sheet
            ExcelReader excelReader = ExcelUtil.getReader(inputStream);
            //可以加上表头验证
            //3.读取第二行到最后一行数据
            List<List<Object>> read = excelReader.read(1, excelReader.getRowCount());
            for (List<Object> objects : read) {
                OrderBatchDeliverDTO orderBatchDeliverDTO = new OrderBatchDeliverDTO();
                orderBatchDeliverDTO.setOrderSn(objects.get(0).toString());
                orderBatchDeliverDTO.setLogisticsName(objects.get(1).toString());
                orderBatchDeliverDTO.setLogisticsNo(objects.get(2).toString());
                orderBatchDeliverDTOList.add(orderBatchDeliverDTO);
            }
        } catch (Exception e) {
            throw new ServiceException(ResultCode.ORDER_LOGISTICS_NOT_COMPLETE);
        }
        //循环检查是否符合规范
        checkBatchDeliver(orderBatchDeliverDTOList);
        //订单批量发货
        for (OrderBatchDeliverDTO orderBatchDeliverDTO : orderBatchDeliverDTOList) {
            OrderDetailVO orderDetailVO = this.queryDetail(orderBatchDeliverDTO.getOrderSn());
            PartDeliveryParamsDTO partDeliveryParamsDTO =  new PartDeliveryParamsDTO(orderDetailVO);
            partDeliveryParamsDTO.setLogisticsNo(orderBatchDeliverDTO.getLogisticsNo());
            partDeliveryParamsDTO.setLogisticsId(orderBatchDeliverDTO.getLogisticsId());
            this.partDelivery(partDeliveryParamsDTO);
        }
    }


    @Override
    public Page<PaymentLog> queryPaymentLogs(Page<PaymentLog> page, Wrapper<PaymentLog> queryWrapper) {
        return baseMapper.queryPaymentLogs(page, queryWrapper);
    }

    private void cancelOrder(String orderSn, String cancelReason) {
        // 修改订单状态
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getSn, orderSn);
        updateWrapper.set(Order::getOrderStatus, OrderStatusEnum.CANCELLED.name());
        updateWrapper.set(Order::getCancelReason, cancelReason);

        // 更新订单项售后状态
        LambdaUpdateWrapper<OrderItem> orderItemLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        orderItemLambdaUpdateWrapper.eq(OrderItem::getOrderSn, orderSn);
        orderItemLambdaUpdateWrapper.set(OrderItem::getAfterSaleStatus, OrderItemAfterSaleStatusEnum.EXPIRED.name());
        orderItemLambdaUpdateWrapper.set(OrderItem::getComplainStatus, OrderComplaintStatusEnum.EXPIRED.name());
        orderItemService.update(orderItemLambdaUpdateWrapper);
        //判断是否存在用户，如果不存在是从consumer过来的内容，已经保存过日志，不在进行二次保存
        AuthUser currentUser = UserContext.getCurrentUser();
        if (currentUser != null) {
            OrderLog orderLog = new OrderLog(orderSn, currentUser.getId(), currentUser.getScene().value(),
                    UserContext.getCurrentUser().getScene().equals(SceneEnums.MEMBER) ? UserContext.getCurrentUser().getNickName() : UserContext.getCurrentUser().getExtendName(), "订单取消，取消原因：" + cancelReason);
            orderLogService.save(orderLog);
        }


        //修改订单
        this.update(updateWrapper);
    }

    /**
     * 循环检查批量发货订单列表
     *
     * @param list 待发货订单列表
     */
    private void checkBatchDeliver(List<OrderBatchDeliverDTO> list) {

        List<Logistics> logistics = logisticsClient.list();
        for (OrderBatchDeliverDTO orderBatchDeliverDTO : list) {
            //查看订单号是否存在-是否是当前店铺的订单
            Order order =
                    this.getOne(new LambdaQueryWrapper<Order>().eq(Order::getStoreId, UserContext.getCurrentExistUser().getExtendId()).eq(Order::getSn,
                            orderBatchDeliverDTO.getOrderSn()));
            if (order == null) {
                throw new ServiceException("订单编号：'" + orderBatchDeliverDTO.getOrderSn() + " '不存在");
            } else if (!order.getOrderStatus().equals(OrderStatusEnum.UNDELIVERED.name())) {
                throw new ServiceException("订单编号：'" + orderBatchDeliverDTO.getOrderSn() + " '不能发货");
            }
            //获取物流公司
            logistics.forEach(item -> {
                if (item.getName().equals(orderBatchDeliverDTO.getLogisticsName())) {
                    orderBatchDeliverDTO.setLogisticsId(item.getId());
                }
            });
            if (CharSequenceUtil.isEmpty(orderBatchDeliverDTO.getLogisticsId())) {
                throw new ServiceException("物流公司：'" + orderBatchDeliverDTO.getLogisticsName() + " '不存在");
            }
        }


    }

    /**
     * 检查是否开始虚拟成团
     *
     * @param pintuanId   拼团活动id
     * @param requiredNum 成团人数
     * @param fictitious  是否开启成团
     * @return 是否成功
     */
    @Override
    @Transactional
    public boolean checkFictitiousOrder(String pintuanId, Integer requiredNum, Boolean fictitious) {
        Map<String, List<Order>> collect = this.queryListByPromotion(pintuanId).stream().collect(Collectors.groupingBy(Order::getParentOrderSn));

        for (Map.Entry<String, List<Order>> entry : collect.entrySet()) {
            //是否开启虚拟成团
            if (Boolean.FALSE.equals(fictitious)) {
                //如果未开启虚拟成团，则自动取消订单
                String reason = "拼团活动结束订单未成团，系统自动取消订单";
                if (CharSequenceUtil.isNotEmpty(entry.getKey())) {
                    this.systemCancel(entry.getKey(), reason, true);
                } else {
                    for (Order order : entry.getValue()) {
                        if (!CharSequenceUtil.equalsAny(order.getOrderStatus(),
                                OrderStatusEnum.COMPLETED.name(),
                                OrderStatusEnum.DELIVERED.name(),
                                OrderStatusEnum.TAKE.name(),
                                OrderStatusEnum.STAY_PICKED_UP.name(),
                                OrderStatusEnum.PARTS_DELIVERED.name(),
                                OrderStatusEnum.UNDELIVERED.name())) {
                            this.systemCancel(order.getSn(), reason, true);
                        }
                    }
                }
            } else if (Boolean.TRUE.equals(fictitious)) {
                this.fictitiousPintuan(entry);
            }
        }
        return false;
    }

    @Override
    public Map<String, Long> pendingPaymentOrderNum(String supplierId) {
        Map<String, Long> map = new HashMap<>();
        //待付款订单
        Long pendingPaymentOrderNum = this.count(new LambdaQueryWrapper<Order>().eq(Order::getStoreId, supplierId).eq(Order::getOrderStatus,
                OrderStatusEnum.UNPAID.name()));
        map.put("pendingPaymentOrderNum", pendingPaymentOrderNum);
        //待发货,待收货订单
        Long deliveryAndReceiptOrderNum =
                this.count(new LambdaQueryWrapper<Order>().eq(Order::getStoreId, supplierId).and(i -> i.eq(Order::getOrderStatus,
                        OrderStatusEnum.UNDELIVERED.name()).or().eq(Order::getOrderStatus, OrderStatusEnum.DELIVERED.name())));
        map.put("deliveryAndReceiptOrderNum", deliveryAndReceiptOrderNum);
        //退款 退货订单
        Long refundAndReturnOrderNum = this.count(new LambdaQueryWrapper<Order>().eq(Order::getStoreId, supplierId).eq(Order::getOrderStatus,
                OrderStatusEnum.CANCELLED.name()));
        map.put("refundAndReturnOrderNum", refundAndReturnOrderNum);
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Order partDelivery(PartDeliveryParamsDTO partDeliveryParamsDTO) {
        String logisticsId = partDeliveryParamsDTO.getLogisticsId();
        String orderSn = partDeliveryParamsDTO.getOrderSn();
        String invoiceNumber = partDeliveryParamsDTO.getLogisticsNo();
        Order order = this.checkDeliver(orderSn);
        //校验订单是否可以发货
        List<OrderFlow> orderFlowList = orderFlowService.list(new QueryWrapper<OrderFlow>().eq("order_sn", order.getSn()));
        if (orderFlowList != null) {
            orderFlowList.forEach(item -> {
                if (!item.verify()) {
                    throw new ServiceException(ResultCode.ORDER_VERIFY_ERROR, item.verifyMessage());
                }
            });
        }
        //获取对应物流
        Logistics logistics = logisticsClient.getById(logisticsId);
        if (logistics == null) {
            throw new ServiceException(ResultCode.ORDER_LOGISTICS_ERROR);
        }
        if (order.getDeliverStatus().equals(DeliverStatusEnum.UNDELIVERED.name()) && (order.getOrderStatus().equals(OrderStatusEnum.UNDELIVERED.name()) || order.getOrderStatus().equals(OrderStatusEnum.PARTS_DELIVERED.name()))) {
            // 补充包裹信息
            OrderPackage orderPackage = new OrderPackage();
            orderPackage.setPackageNo(SnowFlake.createStr("OP"));
            orderPackage.setOrderSn(orderSn);
            orderPackage.setLogisticsNo(invoiceNumber);
            orderPackage.setLogisticsCode(logistics.getId());
            orderPackage.setLogisticsName(logistics.getName());
            orderPackage.setStatus("1");
            orderPackage.setConsigneeMobile(order.getConsigneeMobile());
            orderPackageService.save(orderPackage);
            // TODO 快递鸟物流订阅
            if (partDeliveryParamsDTO.isLogisticsPush()) {
                 // kdBirdService.subscribeTo(logistics.getCode(), invoiceNumber, orderPackage.getPackageNo());
            }
            //完善子订单信息
            List<OrderItem> orderItemList = orderItemService.getByOrderSn(orderSn);

            for (PartDeliveryDTO partDeliveryDTO : partDeliveryParamsDTO.getPartDeliveryDTOList()) {
                for (OrderItem orderItem : orderItemList) {
                    //寻找订单货物进行判断
                    if (partDeliveryDTO.getOrderItemId().equals(orderItem.getId())) {
                        if ((partDeliveryDTO.getDeliveryNum() + orderItem.getDeliverNumber()) > orderItem.getNum()) {
                            throw new ServiceException("发货数量不正确!");
                        }
                        orderItem.setDeliverNumber((partDeliveryDTO.getDeliveryNum() + orderItem.getDeliverNumber()));

                        // 记录分包裹中每个item子单的具体发货信息
                        OrderPackageItem orderPackageItem = new OrderPackageItem();
                        orderPackageItem.setOrderSn(orderSn);
                        orderPackageItem.setPackageNo(orderPackage.getPackageNo());
                        orderPackageItem.setOrderItemSn(orderItem.getSn());
                        orderPackageItem.setDeliverNumber(partDeliveryDTO.getDeliveryNum());
                        orderPackageItem.setLogisticsTime(new Date());
                        orderPackageItem.setGoodsName(orderItem.getGoodsName());
                        orderPackageItem.setThumbnail(orderItem.getImage());
                        orderPackageItemService.save(orderPackageItem);
                    }
                }
            }
            //修改订单货物
            orderItemService.updateBatchById(orderItemList);
            String userName = "系统操作", id = "-1", role = SceneEnums.SYSTEM.value();
            if (UserContext.getCurrentUser() != null) {
                //日志对象拼接
                userName = UserContext.getCurrentUser().getUsername();
                id = UserContext.getCurrentUser().getId();
                role = UserContext.getCurrentUser().getScene().value();
            }
            OrderLog orderLog = new OrderLog(orderSn, id, role, userName, "订单[" + orderSn + "]分包裹发货，发货单号[" + invoiceNumber + "]");
            orderLogService.save(orderLog);
            //判断订单货物是否全部发货完毕
            boolean delivery = true;
            for (OrderItem orderItem : orderItemList) {
                if (orderItem.getDeliverNumber() < orderItem.getNum()) {
                    delivery = false;
                    break;
                }
            }
            //是否全部发货
            if (delivery) {
                return delivery(orderSn, invoiceNumber, logisticsId);
            } else {
                order.setOrderStatus(OrderStatusEnum.PARTS_DELIVERED.name());
                //修改订单货物可以进行售后、投诉
                orderItemService.update(new UpdateWrapper<OrderItem>().eq(ORDER_SN_COLUMN, orderSn).eq("after_sale_status", OrderItemAfterSaleStatusEnum.NEW).set("after_sale_status",
                        OrderItemAfterSaleStatusEnum.NOT_APPLIED).set("complain_status", OrderComplaintStatusEnum.NO_APPLY));
                this.updateById(order);
            }
        }

        return order;
    }

    /**
     * 虚拟成团
     *
     * @param entry       订单列表
     */
    @Transactional
    public void fictitiousPintuan(Map.Entry<String, List<Order>> entry) {
        Map<String, List<Order>> listMap = entry.getValue().stream().collect(Collectors.groupingBy(Order::getPayStatus));
        //未付款订单
        List<Order> unpaidOrders = listMap.get(PayStatusEnum.UNPAID.name());
        //未付款订单自动取消
        if (unpaidOrders != null && !unpaidOrders.isEmpty()) {
            for (Order unpaidOrder : unpaidOrders) {
                this.systemCancel(unpaidOrder.getSn(), "拼团活动结束订单未付款，系统自动取消订单", true);
            }
        }
        List<Order> paidOrders = listMap.get(PayStatusEnum.PAID.name());
        //如待参团人数大于0，并已开启虚拟成团
        if (!paidOrders.isEmpty()) {
            for (Order paidOrder : paidOrders) {
                if (!CharSequenceUtil.equalsAny(paidOrder.getOrderStatus(), OrderStatusEnum.COMPLETED.name(), OrderStatusEnum.DELIVERED.name(),
                        OrderStatusEnum.TAKE.name(), OrderStatusEnum.STAY_PICKED_UP.name())) {
                    if (OrderTypeEnum.NORMAL.name().equals(paidOrder.getOrderType())) {
                        paidOrder.setOrderStatus(OrderStatusEnum.UNDELIVERED.name());
                    } else if (OrderTypeEnum.VIRTUAL.name().equals(paidOrder.getOrderType())) {
                        paidOrder.setOrderStatus(OrderStatusEnum.TAKE.name());
                    }
                    this.updateById(paidOrder);
                    orderStatusMessage(paidOrder);
                }
            }
        }
    }

    /**
     * 订单状态变更消息
     *
     * @param order 订单信息
     */
    @Transactional
    public void orderStatusMessage(Order order) {
        OrderMessage orderMessage = new OrderMessage();
        orderMessage.setOrderSn(order.getSn());
        orderMessage.setNewStatus(OrderStatusEnum.valueOf(order.getOrderStatus()));
        this.sendUpdateStatusMessage(orderMessage);
    }


    /**
     * 此方法只提供内部调用，调用前应该做好权限处理
     * 修改订单状态为已发货
     *
     * @param orderSn     订单编号
     */
    private void updateStatus(String orderSn) {
        this.baseMapper.updateStatus(OrderStatusEnum.DELIVERED.name(), orderSn);
    }

    /**
     * 检测拼团订单内容
     * 此方法用与订单确认
     * 判断拼团是否达到人数进行下一步处理
     *
     * @param pintuanId     拼团活动ID
     * @param parentOrderSn 拼团父订单编号
     */
    @Transactional
    public void checkPintuanOrder(String pintuanId, String parentOrderSn) {
        //获取拼团配置
        Pintuan pintuan = promotionsClient.getPintuanVO(pintuanId);
        List<Order> list = this.getPintuanOrder(pintuanId, parentOrderSn);
        int count = list.size();
        if (count == 1) {
            //如果为开团订单，则发布一个24小时的延时任务，时间到达后，如果未成团则自动结束（未开启虚拟成团的情况下）
            PintuanOrderMessage pintuanOrderMessage = new PintuanOrderMessage();
            //开团结束时间
            long startTime = DateUtil.offsetHour(new Date(), 24).getTime();
            if (DateUtil.compare(DateUtil.offsetHour(pintuan.getOriginStartTime(), 24), pintuan.getOriginEndTime()) > 0) {
                startTime = pintuan.getOriginEndTime().getTime();
            }
            pintuanOrderMessage.setOrderSn(parentOrderSn);
            pintuanOrderMessage.setPintuanId(pintuanId);

            TimeTriggerMsg timeTriggerMsg =
                    TimeTriggerMsg.builder().
                            triggerExecutor(TimeExecuteConstant.PROMOTION_EXECUTOR).
                            triggerTime(startTime).
                            param(pintuanOrderMessage).
                            uniqueKey(DelayQueueTools.wrapperUniqueKey(DelayTypeEnums.PINTUAN_ORDER, (pintuanId + parentOrderSn))).
                            exchange(amqpExchangeProperties.getPromotion()).
                            routingKey(PromotionRoutingKey.PINTUAN).build();

            this.timeTrigger.addDelay(timeTriggerMsg);
        }
        //拼团所需人数，小于等于 参团后的人数，则说明成团，所有订单成团
        if (pintuan.getRequiredNum() <= count) {
            this.pintuanOrderSuccess(list);
        }
    }

    /**
     * 根据拼团活动id和拼团订单sn获取所有当前与当前拼团订单sn相关的订单
     *
     * @param pintuanId     拼团活动id
     * @param parentOrderSn 拼团订单sn
     * @return 所有当前与当前拼团订单sn相关的订单
     */
    private List<Order> getPintuanOrder(String pintuanId, String parentOrderSn) {
        //寻找拼团的所有订单
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getPromotionId, pintuanId).eq(Order::getOrderPromotionType, OrderPromotionTypeEnum.PINTUAN.name()).eq(Order::getPayStatus, PayStatusEnum.PAID.name());
        //拼团sn=开团订单sn 或者 参团订单的开团订单sn
        queryWrapper.and(i -> i.eq(Order::getSn, parentOrderSn).or(j -> j.eq(Order::getParentOrderSn, parentOrderSn)));
        queryWrapper.ne(Order::getOrderStatus, OrderStatusEnum.CANCELLED.name());
        //参团后的订单数（人数）
        return this.list(queryWrapper);
    }

    /**
     * 根据提供的拼团订单列表更新拼团状态为拼团成功
     * 循环订单列表根据不同的订单类型进行确认订单
     *
     * @param orderList 需要更新拼团状态为成功的拼团订单列表
     */
    @Transactional
    public void pintuanOrderSuccess(List<Order> orderList) {
        for (Order order : orderList) {
            if (order.getOrderType().equals(OrderTypeEnum.VIRTUAL.name())) {
                this.virtualOrderConfirm(order.getSn());
            } else if (order.getOrderType().equals(OrderTypeEnum.NORMAL.name())) {
                this.normalOrderConfirm(order.getSn());
            }
        }
    }

    /**
     * 根据提供的拼团订单列表更新拼团状态为拼团失败
     *
     * @param parentOrderSn 拼团订单sn
     */
    private void pintuanOrderFailed(String parentOrderSn) {
        List<Order> list = this.list(new LambdaQueryWrapper<Order>().eq(Order::getParentOrderSn, parentOrderSn).or().eq(Order::getSn, parentOrderSn));
        for (Order order : list) {
            try {
                this.systemCancel(order.getSn(), "拼团人数不足，拼团失败！", true);
            } catch (Exception e) {
                log.error("拼团订单取消失败", e);
            }
        }
    }


    /**
     * 检查交易信息
     *
     * @param tradeDTO 交易DTO
     */
    private void checkTradeDTO(TradeDTO tradeDTO) {
        //检测是否为拼团订单
        if (tradeDTO.getParentOrderSn() != null) {
            //判断用户不能参与自己发起的拼团活动
            LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Order::getSn, tradeDTO.getParentOrderSn());
            Order parentOrder = this.getOne(queryWrapper, false);
            if (parentOrder.getBuyerId().equals(UserContext.getCurrentExistUser().getExtendId())) {
                throw new ServiceException(ResultCode.PINTUAN_JOIN_ERROR);
            }
        }
    }

    /**
     * 普通商品订单确认
     * 修改订单状态为待发货
     * 发送订单状态变更消息
     *
     * @param orderSn 订单编号
     */
    @Transactional
    public void normalOrderConfirm(String orderSn) {
        //修改订单
        this.update(new LambdaUpdateWrapper<Order>().eq(Order::getSn, orderSn).set(Order::getOrderStatus, OrderStatusEnum.UNDELIVERED.name()));
        //修改订单
        OrderMessage orderMessage = new OrderMessage();
        orderMessage.setNewStatus(OrderStatusEnum.UNDELIVERED);
        orderMessage.setOrderSn(orderSn);
        this.sendUpdateStatusMessage(orderMessage);
    }

    /**
     * 虚拟商品订单确认
     * 修改订单状态为待核验
     * 发送订单状态变更消息
     *
     * @param orderSn 订单编号
     */
    @Transactional
    public void virtualOrderConfirm(String orderSn) {
        Order order = this.getBySn(orderSn);
        if (order != null && !OrderStatusEnum.COMPLETED.name().equals(order.getOrderStatus())) {
            //修改订单
            this.update(new LambdaUpdateWrapper<Order>().eq(Order::getSn, orderSn)
                    .set(Order::getOrderStatus, OrderStatusEnum.TAKE.name()));
            OrderMessage orderMessage = new OrderMessage();
            orderMessage.setNewStatus(OrderStatusEnum.TAKE);
            orderMessage.setOrderSn(orderSn);
            this.sendUpdateStatusMessage(orderMessage);
        }
    }

    /**
     * 检测虚拟订单信息
     *
     * @param order            订单
     * @param verificationCode 验证码
     */
    private void checkVerificationOrder(Order order, String verificationCode) {
        //判断查询是否可以查询到订单
        if (order == null) {
            throw new ServiceException(ResultCode.ORDER_NOT_EXIST);
        }
        //判断是否为虚拟订单
        if (!order.getOrderType().equals(OrderTypeEnum.VIRTUAL.name()) && !order.getDeliveryMethod().equals(DeliveryMethodEnum.SELF_PICK_UP.name())) {
            throw new ServiceException(ResultCode.ORDER_TAKE_ERROR);
        }
        //判断虚拟订单状态
        if (!order.getOrderStatus().equals(OrderStatusEnum.TAKE.name()) && !order.getOrderStatus().equals(OrderStatusEnum.STAY_PICKED_UP.name())) {
            throw new ServiceException(ResultCode.ORDER_TAKE_ERROR);
        }
        //判断验证码是否正确
        if (!verificationCode.equals(order.getVerificationCode())) {
            throw new ServiceException(ResultCode.ORDER_TAKE_ERROR);
        }
    }


    /**
     * 初始化填充订单导出数据
     *
     * @param orderExportDTOList 导出的订单数据
     * @return 订单导出列表
     */
    private XSSFWorkbook initOrderExportData(List<OrderExportDTO> orderExportDTOList) {
        List<OrderExportDetailDTO> orderExportDetailDTOList = new ArrayList<>();
        for (OrderExportDTO orderExportDTO : orderExportDTOList) {
            OrderExportDetailDTO orderExportDetailDTO = new OrderExportDetailDTO();
            BeanUtil.copyProperties(orderExportDTO, orderExportDetailDTO);
            //金额
            PriceDetailDTO priceDetailDTO = GsonUtils.fromJson(orderExportDTO.getPriceDetail(), PriceDetailDTO.class);
            orderExportDetailDTO.setFreightPrice(priceDetailDTO.getFreightPrice());
            orderExportDetailDTO.setDiscountPrice(CurrencyUtil.add(priceDetailDTO.getDiscountPrice(), priceDetailDTO.getCouponPrice()));
            orderExportDetailDTO.setUpdatePrice(priceDetailDTO.getUpdatePrice());
            orderExportDetailDTO.setSiteMarketingCost(priceDetailDTO.getStoreMarketingCost());
            orderExportDetailDTO.setStoreMarketingCost(CurrencyUtil.sub(orderExportDetailDTO.getDiscountPrice(), orderExportDetailDTO.getSiteMarketingCost()));
            //地址
            if(StrUtil.isNotBlank(orderExportDTO.getConsigneeAddressPath())){
                String[] receiveAddress = orderExportDTO.getConsigneeAddressPath().split(",");
                orderExportDetailDTO.setProvince(receiveAddress[0]);
                orderExportDetailDTO.setCity(receiveAddress[1]);
                orderExportDetailDTO.setDistrict(receiveAddress.length>2?receiveAddress[2]:"");
                orderExportDetailDTO.setStreet(receiveAddress.length>3?receiveAddress[3]:"");
            }

            //状态
            orderExportDetailDTO.setOrderStatus(OrderStatusEnum.valueOf(orderExportDTO.getOrderStatus()).description());
            orderExportDetailDTO.setPaymentMethod(CharSequenceUtil.isNotBlank(orderExportDTO.getPaymentMethod()) ? PaymentMethodEnum.valueOf(orderExportDTO.getPaymentMethod()).paymentName() : "");
            orderExportDetailDTO.setClientType(ClientTypeEnum.valueOf(orderExportDTO.getClientType()).value());
            orderExportDetailDTO.setOrderType(OrderTypeEnum.valueOf(orderExportDTO.getOrderType()).description());
            orderExportDetailDTO.setAfterSaleStatus(OrderItemAfterSaleStatusEnum.valueOf(orderExportDTO.getAfterSaleStatus()).description());

            //时间
            orderExportDetailDTO.setCreateTime(DateUtil.formatDateTime(orderExportDTO.getCreateTime()));
            orderExportDetailDTO.setPaymentTime(DateUtil.formatDateTime(orderExportDTO.getPaymentTime()));
            orderExportDetailDTO.setLogisticsTime(DateUtil.formatDateTime(orderExportDTO.getLogisticsTime()));
            orderExportDetailDTO.setCompleteTime(DateUtil.formatDateTime(orderExportDTO.getCompleteTime()));
            orderExportDetailDTOList.add(orderExportDetailDTO);
        }

        XSSFWorkbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("订单列表");

        // 创建表头
        Row header = sheet.createRow(0);
        String[] headers = {"主订单编号", "子订单编号", "选购商品", "商品数量", "商品ID", "商品单价", "订单应付金额",
                "运费", "优惠总金额", "平台优惠", "商家优惠", "商家改价", "支付方式", "收件人", "收件人手机号",
                "省", "市", "区", "街道", "详细地址", "买家留言", "订单提交时间", "支付完成时间", "来源",
                "订单状态", "订单类型", "售后状态", "取消原因", "发货时间", "完成时间", "店铺"};

        for (int i = 0; i < headers.length; i++) {
            Cell cell = header.createCell(i);
            cell.setCellValue(headers[i]);
        }
        // 填充数据
        for (int i = 0; i < orderExportDetailDTOList.size(); i++) {
            OrderExportDetailDTO dto = orderExportDetailDTOList.get(i);
            Row row = sheet.createRow(i + 1);
            row.createCell(0).setCellValue(dto.getOrderSn());
            row.createCell(1).setCellValue(dto.getOrderItemSn());
            row.createCell(2).setCellValue(dto.getGoodsName());
            row.createCell(3).setCellValue(dto.getNum());
            row.createCell(4).setCellValue(dto.getGoodsId());
            row.createCell(5).setCellValue(dto.getUnitPrice() == null ? 0 : dto.getUnitPrice());
            row.createCell(6).setCellValue(dto.getFlowPrice());
            row.createCell(7).setCellValue(dto.getFreightPrice());
            row.createCell(8).setCellValue(dto.getDiscountPrice());
            row.createCell(9).setCellValue(dto.getSiteMarketingCost());
            row.createCell(10).setCellValue(dto.getStoreMarketingCost());
            row.createCell(11).setCellValue(dto.getUpdatePrice());
            row.createCell(12).setCellValue(dto.getPaymentMethod());
            row.createCell(13).setCellValue(dto.getConsigneeName());
            row.createCell(14).setCellValue(dto.getConsigneeMobile());
            row.createCell(15).setCellValue(dto.getProvince());
            row.createCell(16).setCellValue(dto.getCity());
            row.createCell(17).setCellValue(dto.getDistrict());
            row.createCell(18).setCellValue(dto.getStreet());
            row.createCell(19).setCellValue(dto.getConsigneeDetail());
            row.createCell(20).setCellValue(dto.getRemark());
            row.createCell(21).setCellValue(dto.getCreateTime());
            row.createCell(22).setCellValue(dto.getPaymentTime());
            row.createCell(23).setCellValue(dto.getClientType());
            row.createCell(24).setCellValue(dto.getOrderStatus());
            row.createCell(25).setCellValue(dto.getOrderType());
            row.createCell(26).setCellValue(dto.getAfterSaleStatus());
            row.createCell(27).setCellValue(dto.getCancelReason());
            row.createCell(28).setCellValue(dto.getLogisticsTime());
            row.createCell(29).setCellValue(dto.getCompleteTime());
            row.createCell(30).setCellValue(dto.getStoreName());
        }

        //修改列宽
//        sheet.setColumnWidth(0, 30 * 256);
//        sheet.setColumnWidth(1, 30 * 256);
//        sheet.setColumnWidth(2, 30 * 256);
//        sheet.setColumnWidth(3, 8 * 256);
//        sheet.setColumnWidth(4, 20 * 256);
//        sheet.setColumnWidth(5, 10 * 256);
//        sheet.setColumnWidth(6, 10 * 256);
//        sheet.setColumnWidth(7, 10 * 256);
//        sheet.setColumnWidth(8, 10 * 256);
//        sheet.setColumnWidth(9, 10 * 256);
//        sheet.setColumnWidth(10, 10 * 256);
//        sheet.setColumnWidth(11, 10 * 256);
//        sheet.setColumnWidth(12, 10 * 256);
//        sheet.setColumnWidth(13, 10 * 256);
//        sheet.setColumnWidth(14, 16 * 256);
//        sheet.setColumnWidth(15, 10 * 256);
//        sheet.setColumnWidth(16, 10 * 256);
//        sheet.setColumnWidth(17, 10 * 256);
//        sheet.setColumnWidth(18, 10 * 256);
//        sheet.setColumnWidth(19, 30 * 256);
//        sheet.setColumnWidth(20, 20 * 256);
//        sheet.setColumnWidth(21, 20 * 256);
//        sheet.setColumnWidth(22, 20 * 256);
//        sheet.setColumnWidth(23, 10 * 256);
//        sheet.setColumnWidth(24, 10 * 256);
//        sheet.setColumnWidth(25, 10 * 256);
//        sheet.setColumnWidth(26, 10 * 256);
//        sheet.setColumnWidth(27, 20 * 256);
//        sheet.setColumnWidth(28, 20 * 256);
//        sheet.setColumnWidth(29, 20 * 256);
//        sheet.setColumnWidth(30, 20 * 256);
        return workbook;
    }

    /**
     * 查询当天订单完成列表
     * @return 订单列表
     */
    @Override
    public List<Order> queryTodayCompleteOrderList() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String date = simpleDateFormat.format(new Date());
        //查询当天完成订单列表
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        queryWrapper.likeRight("complete_time",date);
        queryWrapper.eq("order_status","DELIVERED");//交付订单
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<OrderTopVo> orderTop(List<String> operatedIds) {
        // 获取已支付自营商品订单
        List<PayOrderTopDto> orders = this.baseMapper.orderTop(operatedIds);
        if (orders.isEmpty()) {
            return List.of();
        }
        List<OrderTopVo> list = new ArrayList<>();
        for (PayOrderTopDto order : orders) {
            // 根据商品ID获取商品分红和类型
            GoodsTopDto goodsTopDto = goodsClient.goodsTop(order.getGoodsId());
            if (goodsTopDto != null) {
                OrderTopVo dto = new OrderTopVo();
                dto.setPrice(order.getFlowPrice());
                dto.setUsername(order.getUsername());
                // 分红
                dto.setSplitRatio(goodsTopDto.getSplitRatio());
                dto.setType(goodsTopDto.getType());
                dto.setIcon(goodsTopDto.getIcon());
                list.add(dto);
            }
        }
        return list;
    }

    /**
     * 处理三方订单创建
     *
     * @param order 订单信息
     */
    private void processThirdPartyOrderCreation(Order order) {
        try {
            // 获取订单项
            List<OrderItem> orderItems = orderItemService.getByOrderSn(order.getSn());
            if (orderItems == null || orderItems.isEmpty()) {
                log.debug("订单无商品项，跳过三方订单创建，订单号: {}", order.getSn());
                return;
            }

            // 筛选出三方商品（有supplier_sku_id的商品）
            List<OrderItem> thirdPartyItems = orderItems.stream()
                    .filter(item -> item.getSupplierSkuId() != null && !item.getSupplierSkuId().trim().isEmpty())
                    .collect(Collectors.toList());

            if (thirdPartyItems.isEmpty()) {
                log.debug("订单无三方商品，跳过三方订单创建，订单号: {}", order.getSn());
                return;
            }

            log.info("发现三方商品，开始创建三方订单，订单号: {}, 三方商品数量: {}", order.getSn(), thirdPartyItems.size());

            // 按供应商分组处理
            Map<String, List<OrderItem>> supplierGroups = thirdPartyItems.stream()
                    .collect(Collectors.groupingBy(item -> {
                        // 这里可以根据商品的供应商信息进行分组
                        // 暂时假设都是云中鹤供应商
                        return "YZH";
                    }));

            // 处理每个供应商的订单
            for (Map.Entry<String, List<OrderItem>> entry : supplierGroups.entrySet()) {
                String supplierType = entry.getKey();
                List<OrderItem> supplierItems = entry.getValue();

                try {
                    if ("YZH".equals(supplierType)) {
                        // 调用云中鹤订单创建服务
                        createYzhThirdPartyOrder(order, supplierItems);
                    }
                    // 这里可以扩展其他供应商的处理逻辑
                } catch (Exception e) {
                    log.error("创建{}供应商三方订单失败，订单号: {}, 异常: {}",
                             supplierType, order.getSn(), e.getMessage(), e);
                }
            }

        } catch (Exception e) {
            log.error("处理三方订单创建异常，订单号: {}, 异常: {}", order.getSn(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 创建云中鹤三方订单
     */
    private void createYzhThirdPartyOrder(Order order, List<OrderItem> orderItems) {
        try {
            log.info("开始创建云中鹤三方订单，系统订单号: {}", order.getSn());
            yzhThirdPartyOrderService.createThirdPartyOrder(order, orderItems);
            log.info("云中鹤三方订单创建完成，系统订单号: {}", order.getSn());
        } catch (Exception e) {
            log.error("创建云中鹤三方订单失败，系统订单号: {}, 异常: {}", order.getSn(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 处理三方订单取消
     * 在系统订单取消后，判断是否包含三方商品，如果包含则执行三方取消订单操作
     *
     * @param order 系统订单
     * <AUTHOR>
     */
    private void processThirdPartyOrderCancel(Order order) {
        try {
            log.info("开始处理三方订单取消逻辑，系统订单号: {}", order.getSn());

            // 获取订单项
            List<OrderItem> orderItems = orderItemService.getByOrderSn(order.getSn());
            if (orderItems == null || orderItems.isEmpty()) {
                log.debug("订单无商品项，跳过三方订单取消，订单号: {}", order.getSn());
                return;
            }

            // 调用三方订单取消管理器处理
            List<ThirdPartyCancelResult> cancelResults = thirdPartyOrderCancelManager.processOrderCancel(order, orderItems);

            // 记录取消结果
            if (cancelResults != null && !cancelResults.isEmpty()) {
                for (ThirdPartyCancelResult result : cancelResults) {
                    if (result.isSuccess()) {
                        log.info("三方订单取消成功，供应商: {}, 系统订单号: {}, 三方订单ID: {}, 取消状态: {}",
                                result.getSupplier() != null ? result.getSupplier().getDescription() : "未知",
                                result.getSystemOrderSn(), result.getThirdPartyOrderId(), result.getCancelStatus());
                    } else {
                        log.error("三方订单取消失败，供应商: {}, 系统订单号: {}, 错误信息: {}",
                                result.getSupplier() != null ? result.getSupplier().getDescription() : "未知",
                                result.getSystemOrderSn(), result.getErrorMessage());
                    }
                }
            } else {
                log.debug("无三方订单需要取消，订单号: {}", order.getSn());
            }

            log.info("三方订单取消处理完成，系统订单号: {}", order.getSn());

        } catch (Exception e) {
            log.error("处理三方订单取消异常，系统订单号: {}, 异常: {}", order.getSn(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public OrderStatusCountVO getOrderStatusCount(String userId) {
        try {
            log.debug("开始统计用户订单状态数量，用户ID: {}", userId);

            // 参数校验
            if (CharSequenceUtil.isBlank(userId)) {
                log.warn("用户ID为空，返回空统计结果");
                return OrderStatusCountVO.empty();
            }

            // 使用单一SQL查询提高性能
            var statusCountMap = this.baseMapper.countOrderStatusByUserId(userId);

            Long unpaidCount = statusCountMap.getOrDefault("unpaidCount", 0L);
            Long undeliveredCount = statusCountMap.getOrDefault("undeliveredCount", 0L);
            Long deliveredCount = statusCountMap.getOrDefault("deliveredCount", 0L);
            Long unfinishedCommentCount = statusCountMap.getOrDefault("unfinishedCommentCount", 0L);
            Long afterSaleCount = statusCountMap.getOrDefault("afterSaleCount", 0L);
//         待付款-待付款  待发货-待发货   待收货-待发货   待评价：根据订单的商品类型去判断，礼包商品和年卡不能评价 且订单状态是已完成

            // 创建结果对象
            var result = new OrderStatusCountVO(
                    unpaidCount,
                    undeliveredCount,
                    deliveredCount,
                    unfinishedCommentCount,
                    afterSaleCount
            );

            log.info("用户订单状态统计完成，用户ID: {}, 统计结果: 待付款={}, 待发货={}, 待收货={}, 待评价={}, 退换货={}",
                    userId, unpaidCount, undeliveredCount, deliveredCount, unfinishedCommentCount, afterSaleCount);

            return result;

        } catch (Exception e) {
            log.error("统计用户订单状态数量异常，用户ID: {}, 异常: {}", userId, e.getMessage(), e);
            // 发生异常时返回空统计结果，避免接口报错
            return OrderStatusCountVO.empty();
        }
    }
}