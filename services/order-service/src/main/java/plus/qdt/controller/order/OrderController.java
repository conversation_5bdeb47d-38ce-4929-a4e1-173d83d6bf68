package plus.qdt.controller.order;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import plus.qdt.common.aop.annotation.PreventDuplicateSubmissions;
import plus.qdt.common.context.ThreadContextHolder;
import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.member.entity.dto.MemberAddressDTO;
import plus.qdt.modules.order.kd.service.KdBirdService;
import plus.qdt.modules.order.order.entity.dos.Order;
import plus.qdt.modules.order.order.entity.dto.OrderSearchParams;
import plus.qdt.modules.order.order.entity.dto.PartDeliveryParamsDTO;
import plus.qdt.modules.order.order.entity.enums.OrderStatusEnum;
import plus.qdt.modules.order.order.entity.vo.OrderDetailVO;
import plus.qdt.modules.order.order.entity.vo.OrderSimpleVO;
import plus.qdt.modules.order.order.entity.vo.TradeDetailVO;
import plus.qdt.modules.order.order.service.OrderPackageService;
import plus.qdt.modules.order.order.service.OrderPriceService;
import plus.qdt.modules.order.order.service.OrderService;
import plus.qdt.modules.order.order.service.TradeService;
import plus.qdt.modules.store.client.StoreLogisticsClient;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 订单API
 *
 * <AUTHOR>
 * @since 2020/11/17 4:34 下午
 */
@RestController
@RequestMapping("/order")
@Tag(name = "订单API")
@RequiredArgsConstructor
public class OrderController {

    /**
     * 订单
     */
    private final OrderService orderService;

    private final TradeService tradeService;
    /**
     * 订单价格
     */
    private final OrderPriceService orderPriceService;

    private final StoreLogisticsClient storeLogisticsClient;

    private final KdBirdService kdBirdService;

    private final OrderPackageService orderPackageService;


    @Operation(summary = "查询订单")
    @GetMapping
    public ResultMessage<Page<OrderSimpleVO>> queryOrder(OrderSearchParams orderSearchParams) {
        return ResultUtil.data(orderService.queryByParams(orderSearchParams));
    }

    @Operation(summary = "买家订单备注")
    @PutMapping("/{orderSn}/remark")
    public ResultMessage<Object> remark(@PathVariable String orderSn, @RequestParam String remark) {
        orderService.updateRemark(orderSn, remark);
        return ResultUtil.success();
    }

    @Operation(summary = "卖家订单备注")
    @PutMapping("/{orderSn}/sellerRemark")
    public ResultMessage<Object> sellerRemark(@PathVariable String orderSn, @RequestParam String sellerRemark) {
        orderService.updateSellerRemark(orderSn, sellerRemark);
        return ResultUtil.success();
    }


    @Operation(summary = "查询订单导出列表", responses = @ApiResponse(content = {
            @io.swagger.v3.oas.annotations.media.Content(mediaType = "application/octet-stream")
    }))
    @GetMapping("/queryExportOrder")
    public void queryExportOrder(OrderSearchParams orderSearchParams) {
        HttpServletResponse response = ThreadContextHolder.getHttpResponse();
        //校验导出周期
        orderSearchParams.checkoutExportParams();
        orderService.queryExportOrder(response,orderSearchParams);

    }

    @PreventDuplicateSubmissions
    @Operation(summary = "创建电子面单")
    @PostMapping(value = "/{orderSn}/createElectronicsFaceSheet")
    public ResultMessage<Object> createElectronicsFaceSheet(@NotNull(message = "参数非法") @PathVariable String orderSn,
                                                            @NotNull(message = "请选择物流公司") String logisticsId) throws Exception {
        return ResultUtil.data(kdBirdService.createElectronicsFaceSheet(orderSn, logisticsId));
    }

    @Operation(summary = "下载待发货的订单列表", responses = @ApiResponse(content = {
            @io.swagger.v3.oas.annotations.media.Content(mediaType = "application/octet-stream")
    }))
    @GetMapping(value = "/downLoadDeliverExcel")
    public void downLoadDeliverExcel() {
        HttpServletResponse response = ThreadContextHolder.getHttpResponse();
        String storeId = Objects.requireNonNull(UserContext.getCurrentUser()).getExtendId();
        //获取店铺已经选择物流公司列表
        List<String> logisticsName = storeLogisticsClient.getStoreSelectedLogisticsName(storeId);
        //下载订单批量发货Excel
        this.orderService.getBatchDeliverList(response, logisticsName);

    }

    @PostMapping(value = "/batchDeliver", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "上传文件进行订单批量发货")
    public ResultMessage<Object> batchDeliver(@RequestPart("files") MultipartFile files) {
        orderService.batchDeliver(files);
        return ResultUtil.success();
    }


    @Operation(summary = "订单明细")
    @GetMapping(value = "/{orderSn}")
    public ResultMessage<OrderDetailVO> detail(@PathVariable String orderSn) {
        return ResultUtil.data(orderService.queryDetail(orderSn));
    }

    @Operation(summary = "获取交易trade")
    @GetMapping(value = "/getTrade/{orderSn}")
    public ResultMessage<TradeDetailVO> getTrade(@PathVariable String orderSn) {
        return ResultUtil.data(tradeService.getTradeDetail(orderSn));
    }


    @PreventDuplicateSubmissions
    @Operation(summary = "修改收货人信息")
    @PostMapping(value = "/update/{orderSn}/consignee")
    public ResultMessage<Order> consignee(@NotNull(message = "参数非法") @PathVariable String orderSn, @RequestBody @Valid MemberAddressDTO memberAddressDTO) {
        return ResultUtil.data(orderService.updateConsignee(orderSn, memberAddressDTO));
    }

    @PreventDuplicateSubmissions
    @Operation(summary = "修改订单价格")
    @PutMapping(value = "/update/{orderSn}/price")
    public ResultMessage<Order> updateOrderPrice(@PathVariable String orderSn, @NotNull(message = "订单价格不能为空") @RequestParam Double orderPrice) {
        return ResultUtil.data(orderPriceService.updatePrice(orderSn, orderPrice));
    }

    @PreventDuplicateSubmissions
    @Operation(summary = "订单发货")
    @PostMapping(value = "/{orderSn}/delivery")
    public ResultMessage<Object> delivery(@NotNull(message = "参数非法") @PathVariable String orderSn,
                                          @NotNull(message = "发货单号不能为空") String logisticsNo,
                                          @NotNull(message = "请选择物流公司") String logisticsId) {
        return ResultUtil.data(orderService.delivery(orderSn, logisticsNo, logisticsId));
    }

    @Operation(summary = "根据核验码获取订单信息")
    @GetMapping(value = "/getOrderByVerificationCode/{verificationCode}")
    public ResultMessage<Object> getOrderByVerificationCode(@PathVariable String verificationCode) {
        return ResultUtil.data(orderService.getOrderByVerificationCode(verificationCode));
    }

    @PreventDuplicateSubmissions
    @Operation(summary = "订单核验")
    @PutMapping(value = "/take/{orderSn}/{verificationCode}")
    public ResultMessage<Object> take(@PathVariable String orderSn, @PathVariable String verificationCode) {
        return ResultUtil.data(orderService.take(orderSn, verificationCode));
    }

    @PreventDuplicateSubmissions
    @Operation(summary = "取消交易订单", parameters = {
            @Parameter(name = "reason", description = "取消原因", required = true),
            @Parameter(name = "tradeSn", description = "交易订单号", required = true),
    })
    @PostMapping(value = "/{tradeSn}/cancel")
    public ResultMessage<List<Order>> cancel(@PathVariable String tradeSn, @RequestParam String reason) {
        return ResultUtil.data(orderService.cancel(tradeSn, reason));
    }

    @PreventDuplicateSubmissions
    @Operation(summary = "取消订单", parameters = {
            @Parameter(name = "reason", description = "取消原因", required = true),
            @Parameter(name = "orderSn", description = "订单号", required = true),
    })
    @PostMapping(value = "/{orderSn}/cancelOrder")
    public ResultMessage<List<Order>> cancelOrder(@PathVariable String orderSn, @RequestParam String reason) {
        orderService.systemCancel(orderSn, reason, true);
        return ResultUtil.success();
    }

    @PreventDuplicateSubmissions
    @Operation(summary = "删除订单")
    @DeleteMapping(value = "/{orderSn}")
    public ResultMessage<Object> deleteOrder(@PathVariable String orderSn) {
        orderService.getBySn(orderSn);
        orderService.deleteOrder(orderSn);
        return ResultUtil.success();
    }

    @PreventDuplicateSubmissions
    @Operation(summary = "确认收货")
    @PostMapping(value = "/{orderSn}/receiving")
    public ResultMessage<Object> receiving(@NotNull(message = "订单编号不能为空") @PathVariable("orderSn") String orderSn) {
        Order order = orderService.getBySn(orderSn);

        if (order == null) {
            throw new ServiceException(ResultCode.ORDER_NOT_EXIST);
        }
        //判定是否是待收货状态
        if (!order.getOrderStatus().equals(OrderStatusEnum.DELIVERED.name())) {
            throw new ServiceException(ResultCode.ORDER_DELIVERED_ERROR);
        }
        orderService.complete(orderSn);
        return ResultUtil.success();
    }


    @Operation(summary = "查询物流踪迹")
    @PostMapping(value = "/traces/{orderSn}")
    public ResultMessage<Object> getTraces(@NotBlank(message = "订单编号不能为空") @PathVariable String orderSn) {
        return ResultUtil.data(orderService.getTraces(orderSn));
    }

    @PreventDuplicateSubmissions
    @Operation(summary = "开票")
    @PostMapping(value = "/receipt/{orderSn}")
    public ResultMessage<Object> invoice(@NotBlank(message = "订单编号不能为空") @PathVariable String orderSn) {
        return ResultUtil.data(orderService.invoice(orderSn));
    }

    @Operation(summary = "订单包裹发货")
    @PostMapping(value = "/{orderSn}/partDelivery")
    public ResultMessage<Object> delivery(@PathVariable String orderSn, @RequestBody PartDeliveryParamsDTO partDeliveryParamsDTO) {
        partDeliveryParamsDTO.setOrderSn(orderSn);
        return ResultUtil.data(orderService.partDelivery(partDeliveryParamsDTO));
    }

    @Operation(summary = "查看包裹列表")
    @GetMapping(value = "/getPackage/{orderSn}")
    public ResultMessage<Object> getPackage(@NotBlank(message = "订单编号不能为空") @PathVariable String orderSn) {
        return ResultUtil.data(orderPackageService.getOrderPackageVOList(orderSn));
    }

    @Operation(summary = "快递鸟物流订阅回调")
    @PostMapping(value = "/kdBird/callback", consumes = "application/x-www-form-urlencoded")
    public Map<String, Object> kdBirdCallback(@RequestBody String body) {
        return kdBirdService.kdBirdCallback(body);
    }


}