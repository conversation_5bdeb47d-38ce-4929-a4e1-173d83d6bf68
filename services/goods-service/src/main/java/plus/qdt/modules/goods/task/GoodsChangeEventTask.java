package plus.qdt.modules.goods.task;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import plus.qdt.modules.goods.entity.dos.GoodsSku;
import plus.qdt.modules.goods.mapper.GoodsSkuMapper;
import plus.qdt.modules.goods.task.enums.DomainEnum;
import plus.qdt.modules.goods.task.enums.GoodsChangeEventEnum;
import plus.qdt.modules.goods.task.utils.RequestUtil;
import plus.qdt.modules.order.order.entity.dos.Order;
import plus.qdt.modules.order.order.thirdparty.ysy.YsyOrderInfo;
import plus.qdt.modules.payment.client.PaymentClient;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单变化定时
 */
@Slf4j
@Component
public class GoodsChangeEventTask {
    private final DomainEnum domain = DomainEnum.PRODUCTION;
    @Autowired
    private GoodsSkuMapper goodsSkuMapper;

    /**
     * 商品变动事件
     */
    @XxlJob("goodsChangeEvent")
    public void goodsChangeEvent() {
        JSONObject jsonObject = RequestUtil.queryGoodsChangeEvent();
        String msg = jsonObject.getString("msg");
        if ("SUCCESS".equals(msg)){
            JSONArray result = jsonObject.getJSONArray("result");
            for (int i=0;i<result.size();i++){
                JSONObject jsonStr = result.getJSONObject(i);
                extracted(jsonStr);
            }
        }
    }

    /**
     * 商品变更事件，盛创汇联企道通聚合优品
     * @param jsonStr
     */
    private void extracted(JSONObject jsonStr) {
        String eventKey = jsonStr.getString("eventKey");
        if (GoodsChangeEventEnum.GOODS_PRICE.getName().equals(eventKey)){
            //会员价格变动
            JSONArray changesContent = jsonStr.getJSONArray("changesContent");
            for (int i=0;i<changesContent.size();i++){
                JSONObject jsonObject = JSONObject.parseObject(changesContent.get(i).toString());
                String field1 = jsonObject.getString("code");//商品规格编码
                QueryWrapper<GoodsSku> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("supplier_id","1376433565247471617");
                queryWrapper.eq("sn", field1);
                List<GoodsSku> goodsSkus = goodsSkuMapper.selectList(queryWrapper);
                for (GoodsSku goodsSku:goodsSkus){
                    goodsSku.setPromotionPrice(jsonObject.getDouble("price"));//修改会员价格
                    goodsSku.setUpdateTime(new Date());
                    goodsSkuMapper.updateById(goodsSku);
                }
            }
        } else if (GoodsChangeEventEnum.GOODS_UP_STATE.getName().equals(eventKey)){
            //修改是否上架
            JSONArray changesContent = jsonStr.getJSONArray("changesContent");
            for (int j=0;j<changesContent.size();j++){
                JSONObject jsonObject = changesContent.getJSONObject(j);
                Long id = jsonObject.getLong("id");//商品规格编码
                QueryWrapper<GoodsSku> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("supplier_id","1376433565247471617");
                queryWrapper.eq("goods_id", id);
                List<GoodsSku> goodsSkus = goodsSkuMapper.selectList(queryWrapper);
                for (GoodsSku goodsSku:goodsSkus){
                    goodsSku.setMarketEnable("UPPER");//修改商品上架
                    goodsSku.setUpdateTime(new Date());
                    goodsSkuMapper.updateById(goodsSku);
                }
            }
        } else if (GoodsChangeEventEnum.GOODS_DOWN_STATE.getName().equals(eventKey)){
            //修改是否下架
            JSONArray changesContent = jsonStr.getJSONArray("changesContent");
            for (int k=0;k<changesContent.size();k++){
                JSONObject jsonObject = changesContent.getJSONObject(k);
                Long id = jsonObject.getLong("id");//商品规格编码
                QueryWrapper<GoodsSku> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("supplier_id","1376433565247471617");
                queryWrapper.eq("goods_id", id);
                List<GoodsSku> goodsSkus = goodsSkuMapper.selectList(queryWrapper);
                for (GoodsSku goodsSku:goodsSkus){
                    goodsSku.setMarketEnable("DOWN");//修改商品下架
                    goodsSku.setUpdateTime(new Date());
                    goodsSkuMapper.updateById(goodsSku);
                }
            }
        } else if (GoodsChangeEventEnum.GOODS_UPDATE_STOCK.getName().equals(eventKey)){
            //规格库存变动
            JSONArray changesContent = jsonStr.getJSONArray("changesContent");
            for (int g=0;g<changesContent.size();g++){
                JSONObject jsonObject = changesContent.getJSONObject(g);
                String field1 = jsonObject.getString("code");//商品规格编码
                QueryWrapper<GoodsSku> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("supplier_id","1376433565247471617");
                queryWrapper.eq("sn", field1);
                List<GoodsSku> goodsSkus = goodsSkuMapper.selectList(queryWrapper);
                for (GoodsSku goodsSku:goodsSkus){
                    goodsSku.setQuantity(jsonObject.getInteger("stock"));//修改库存
                    goodsSku.setUpdateTime(new Date());
                    goodsSkuMapper.updateById(goodsSku);
                }
            }
        } else if (GoodsChangeEventEnum.GOODS_ADD_ITEM.getName().equals(eventKey)){
            //商品添加规格
            JSONArray changesContent = jsonStr.getJSONArray("changesContent");
            for (int h=0;h<changesContent.size();h++){
                JSONObject jsonObject = changesContent.getJSONObject(h);
                Long id = jsonObject.getLong("goodsId");//商品id
                QueryWrapper<GoodsSku> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("supplier_id","1376433565247471617");
                queryWrapper.eq("goods_id",id);
                List<GoodsSku> qdtGoodsSkus = goodsSkuMapper.selectList(queryWrapper);
                for (GoodsSku goodsSku:qdtGoodsSkus){
                    goodsSku.setSn(jsonObject.getString("code"));//添加商品规格
                    goodsSku.setUpdateTime(new Date());
                    goodsSkuMapper.updateById(goodsSku);
                }
            }
        } else if (GoodsChangeEventEnum.GOODS_REMOVE_ITEM.getName().equals(eventKey)){
            //商品移除规格
            JSONArray changesContent = jsonStr.getJSONArray("changesContent");
            for (int l=0;l<changesContent.size();l++){
                JSONObject jsonObject = changesContent.getJSONObject(l);
                Long id = jsonObject.getLong("goodsId");//商品id
                String code = jsonObject.getString("code");
                QueryWrapper<GoodsSku> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("id", id);
                queryWrapper.eq("sn", code);
                List<GoodsSku> goodsSkus = goodsSkuMapper.selectList(queryWrapper);
                for (GoodsSku goodsSku:goodsSkus){
                    goodsSku.setDeleteFlag(true);//删除该商品
                    goodsSku.setUpdateTime(new Date());
                    goodsSkuMapper.update(queryWrapper);
                }
            }
        }

    }
}
