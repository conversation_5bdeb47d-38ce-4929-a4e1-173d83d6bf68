package plus.qdt.modules.goods.service;

import plus.qdt.modules.goods.entity.dos.Goods;
import plus.qdt.modules.goods.entity.dos.GoodsSku;
import plus.qdt.modules.goods.entity.dto.YZHGoodsSkuDTO;

import java.util.List;

/**
 * 云中鹤商品数据转换服务接口
 *
 * <AUTHOR>
 * @since 2025-06-19
 */
public interface YzhGoodsConvertService {

    /**
     * 将云中鹤商品数据转换为系统内部的 Goods 对象
     *
     * @param yzhGoods 云中鹤商品数据
     * @param userId 用户ID
     * @param scene 场景
     * @param extendId 扩展ID
     * @return 系统内部的 Goods 对象
     */
    Goods convertToGoods(YZHGoodsSkuDTO yzhGoods, String userId, String scene, String extendId);

    /**
     * 将云中鹤商品数据转换为系统内部的 GoodsSku 对象
     *
     * @param yzhGoods 云中鹤商品数据
     * @param goods 对应的 Goods 对象
     * @param userId 用户ID
     * @param scene 场景
     * @param extendId 扩展ID
     * @return 系统内部的 GoodsSku 对象
     */
    GoodsSku convertToGoodsSku(YZHGoodsSkuDTO yzhGoods, Goods goods, String userId, String scene, String extendId);

    /**
     * 批量转换云中鹤商品数据
     *
     * @param yzhGoodsList 云中鹤商品数据列表
     * @param userId 用户ID
     * @param scene 场景
     * @param extendId 扩展ID
     * @return 转换结果，包含 Goods 和 GoodsSku 列表
     */
    ConvertResult batchConvert(List<YZHGoodsSkuDTO> yzhGoodsList, String userId, String scene, String extendId);

    /**
     * 转换结果类
     */
    class ConvertResult {
        private List<Goods> goodsList;
        private List<GoodsSku> goodsSkuList;

        public ConvertResult(List<Goods> goodsList, List<GoodsSku> goodsSkuList) {
            this.goodsList = goodsList;
            this.goodsSkuList = goodsSkuList;
        }

        public List<Goods> getGoodsList() {
            return goodsList;
        }

        public List<GoodsSku> getGoodsSkuList() {
            return goodsSkuList;
        }
    }
}
