package plus.qdt.controller.feign;

import plus.qdt.modules.goods.client.BrandClient;
import plus.qdt.modules.goods.entity.dos.Brand;
import plus.qdt.modules.goods.entity.vos.BrandVO;
import plus.qdt.modules.goods.service.BrandService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @author: ftyy
 * @date: 2022-01-14 10:42
 * @description: 商品品牌Feign
 */
@RestController
@RequiredArgsConstructor
public class BrandFeignController implements BrandClient {
    private final BrandService brandService;

    @Override
    public void deleteBrands(List<String> ids) {
        brandService.deleteBrands(ids);
    }

    @Override
    public List<Brand> getBrandsByCategory(String categoryId) {
        return brandService.getBrandsByCategory(categoryId);
    }

    @Override
    public boolean addBrand(BrandVO brandVO) {
        return brandService.addBrand(brandVO);
    }

    @Override
    public boolean updateBrand(BrandVO brandVO) {
        return brandService.updateBrand(brandVO);
    }

    @Override
    public boolean brandDisable(String brandId, boolean disable) {
        return brandService.brandDisable(brandId, disable);
    }

    @Override
    public Brand getById(String brandId) {
        return brandService.getById(brandId);
    }
}
