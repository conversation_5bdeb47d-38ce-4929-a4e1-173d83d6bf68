package plus.qdt.controller.feign;

import plus.qdt.common.enums.ResultCode;
import plus.qdt.common.exception.ServiceException;
import plus.qdt.common.security.AuthUser;
import plus.qdt.common.security.context.UserContext;
import plus.qdt.common.utils.CurrencyUtil;
import plus.qdt.common.utils.SnowFlake;
import plus.qdt.common.utils.StringUtils;
import plus.qdt.modules.goods.client.GoodsClient;
import plus.qdt.modules.goods.entity.dos.Goods;
import plus.qdt.modules.goods.entity.dos.GoodsSku;
import plus.qdt.modules.goods.entity.dos.Wholesale;
import plus.qdt.modules.goods.entity.dto.GoodsSearchParams;
import plus.qdt.modules.goods.entity.dto.GoodsTopDto;
import plus.qdt.modules.goods.entity.dto.ProxyGoodsOperationDTO;
import plus.qdt.modules.goods.entity.enums.GoodsMarketEnum;
import plus.qdt.modules.goods.entity.enums.GoodsTypeEnum;
import plus.qdt.modules.goods.entity.enums.SalesModeEnum;
import plus.qdt.modules.goods.entity.enums.SupplierEnum;
import plus.qdt.modules.goods.entity.vos.GoodsVO;
import plus.qdt.modules.goods.entity.enums.GoodsType;
import plus.qdt.modules.goods.integration.GoodsIntegrationService;
import plus.qdt.modules.goods.service.GoodsGalleryService;
import plus.qdt.modules.goods.service.GoodsService;
import plus.qdt.modules.goods.service.GoodsSkuService;
import plus.qdt.modules.goods.service.WholesaleService;
import plus.qdt.modules.store.client.FreightTemplateClient;
import plus.qdt.modules.store.client.StoreClient;
import plus.qdt.modules.store.entity.dos.Store;
import plus.qdt.modules.store.entity.vos.FreightTemplateVO;
import plus.qdt.modules.store.entity.vos.StoreVO;
import plus.qdt.modules.supplier.entity.dto.SupplierGoodsOperationDTO;
import plus.qdt.mybatis.util.PageUtil;
import plus.qdt.mybatis.util.SceneHelp;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 商品分类 feign client
 *
 * <AUTHOR>
 * @version v1.0
 * 2021-12-07 06:08
 */
@RestController
@RequiredArgsConstructor
public class GoodsFeignController implements GoodsClient {


    private final GoodsService goodsService;

    private final GoodsSkuService goodsSkuService;

    private final GoodsIntegrationService goodsIntegrationService;

    private final GoodsGalleryService goodsGalleryService;

    private final WholesaleService wholesaleService;

    private final FreightTemplateClient freightTemplateClient;

    private final StoreClient storeClient;

    @Override
    public void addSupplierGoods(SupplierGoodsOperationDTO supplierGoodsOperationDTO) {
        goodsIntegrationService.saveSupplierGoods(supplierGoodsOperationDTO);
    }

    @Override
    public void editSupplierGoods(SupplierGoodsOperationDTO supplierGoodsOperationDTO) {
        goodsIntegrationService.updateSupplierGoods(
                supplierGoodsOperationDTO);
    }

    @Override
    public void batchEditSupplierGoods(List<SupplierGoodsOperationDTO> supplierGoodsOperationDTOList) {
        if (supplierGoodsOperationDTOList != null && !supplierGoodsOperationDTOList.isEmpty()) {
            supplierGoodsOperationDTOList.forEach(this::editSupplierGoods);
        }
    }

    @Override
    public Page<Goods> queryByParams(GoodsSearchParams goodsSearchParams) {
        return goodsService.page(PageUtil.initPage(goodsSearchParams), goodsSearchParams.queryWrapper());
    }

    /**
     * 查询商品VO
     *
     * @param goodsId 商品id
     * @return 商品VO
     */
    @Override
    public GoodsVO getGoodsVO(String goodsId) {
        return goodsIntegrationService.getGoodsVO(goodsId);
    }

    @Override
    public GoodsSku getGoodsSkuByIdFromCache(String skuId) {
        return goodsSkuService.getGoodsSkuByIdFromCache(skuId);
    }

    @Override
    public GoodsSku getCanPromotionGoodsSkuByIdFromCache(String skuId) {
        GoodsSku goodsSku = goodsSkuService.getGoodsSkuByIdFromCache(skuId);
        if (goodsSku != null && SalesModeEnum.WHOLESALE.name().equals(goodsSku.getSalesModel())) {
            throw new ServiceException(ResultCode.PROMOTION_GOODS_DO_NOT_JOIN_WHOLESALE, goodsSku.getGoodsName());
        }
        return goodsSku;
    }

    @Override
    public void updateStoreDetail(Store store) {
        goodsIntegrationService.updateStoreDetail(store);
    }

    @Override
    public void underStoreGoods(String id) {
        goodsIntegrationService.underStoreGoods(id);
    }

    @Override
    public Long count(String storeId) {
        return goodsService.countStoreGoodsNum(storeId);
    }

    @Override
    public Integer getStock(String skuId) {
        return goodsSkuService.getStock(skuId);
    }

    /**
     * 添加商品评价数量
     *
     * @param commentNum 评价数量
     * @param goodsId    商品ID
     */
    @Override
    public void addGoodsCommentNum(Integer commentNum, String goodsId) {
        goodsService.addGoodsCommentNum(commentNum, goodsId);
    }

    @Override
    public Goods getById(String goodsId) {
        return goodsService.getById(goodsId);
    }


    @Override
    public List<Goods> queryListByParams(GoodsSearchParams searchParams) {
        return goodsService.queryListByParams(searchParams);
    }


    @Override
    public List<String> getSkuIdsByGoodsId(String goodsId) {
        return goodsSkuService.getSkuIdsByGoodsId(goodsId);
    }

    @Override
    public Page<GoodsSku> getGoodsSkuByPage(GoodsSearchParams searchParams) {
        return goodsSkuService.getGoodsSkuByPage(searchParams);
    }

    @Override
    public List<GoodsSku> getGoodsSkuByList(GoodsSearchParams searchParams) {
        return goodsSkuService.getGoodsSkuByList(searchParams);
    }

    @Override
    public void updateGoodsBuyCount(String goodsId, int buyCount) {
        goodsService.updateGoodsBuyCount(goodsId, buyCount);
    }

    @Override
    public GoodsSku getGoodsSkuById(String goodsId) {
        return goodsSkuService.getById(goodsId);
    }

    @Override
    public void updateGoodsSku(GoodsSku goodsSku) {
        goodsSkuService.update(goodsSku);
    }

    @Override
    public void batchUpdateGoodsSku(List<GoodsSku> goodsSkuList) {
        if (goodsSkuList != null && !goodsSkuList.isEmpty()) {
            goodsSkuList.forEach(this::updateGoodsSku);
        }
    }

    @Override
    public Goods getGoodsByParams(GoodsSearchParams searchParams) {
        return goodsService.getGoodsByParams(searchParams);
    }

    @Override
    public Wholesale getMatchWholesale(String goodsId, Integer num) {
        return wholesaleService.match(goodsId, num);
    }

    @Override
    public List<Wholesale> getWholesale(String goodsId) {
        return wholesaleService.findByGoodsId(goodsId);
    }

    @Override
    public Long countSkuNum(String storeId) {
        return goodsSkuService.countSkuNum(storeId);
    }

    @Override
    public void deleteGoods(List<String> goodsIds) {
        goodsIntegrationService.deleteGoods(goodsIds);
    }

    @Override
    @Transactional
    public void addSupplierGoods(String goodsId) {
        Goods goods = goodsService.getById(goodsId);
        //商品为空
        if (goods == null) {
            throw new ServiceException(ResultCode.GOODS_NOT_EXIST);
        }
        // 当前用户
        AuthUser currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            throw new ServiceException(ResultCode.USER_NOT_LOGIN);
        }
        StoreVO store = storeClient.getStore(currentUser.getExtendId());

        //判定是否可以代理商品
        if (!store.getBusinessCategory().contains(goods.getCategoryPath().substring(0,
                goods.getCategoryPath().indexOf(",")))) {
            throw new ServiceException(ResultCode.BUSINESS_UN_PROXY);
        }


        //一个店铺只能对一个商品代理一次
        LambdaQueryWrapper<Goods> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Goods::getSupplierGoodsId, goodsId);
        queryWrapper.eq(Goods::getDeleteFlag, false);
        queryWrapper.eq(Goods::getStoreId, currentUser.getExtendId());


        if (this.goodsService.count(queryWrapper) > 0) {
            throw new ServiceException(ResultCode.GOODS_PROXY_TOO_MUCH);
        }
        List<GoodsSku> goodsSkuList = goodsSkuService.getGoodsSkuListByGoodsId(goodsId);
        storeOwnHandler(goods);

        List<FreightTemplateVO> freightTemplateList = freightTemplateClient.getFreightTemplateList(goods.getStoreId());

        // 如果没有运费模板或者商品类型为虚拟商品，则模板id为-1(无运费)
        if (freightTemplateList.isEmpty() || goods.getGoodsType().equals(GoodsTypeEnum.VIRTUAL_GOODS.name())) {
            goods.setTemplateId("-1");
        } else {
            goods.setTemplateId(freightTemplateList.getFirst().getId());
        }

        //处理sku信息
        List<GoodsSku> goodsSkus = storeOwnHandler(goodsSkuList, goods);

        goods.setPrice(goodsSkus.stream().mapToDouble(GoodsSku::getPrice).min().orElse(0.0));

        goodsService.save(goods);
        goodsSkuService.saveBatch(goodsSkus);
    }

    @Override
    public void syncStock(List<String> goodsId) {
        goodsId.forEach(goodsService::syncStock);
    }

    @Override
    public void editProxyGoods(ProxyGoodsOperationDTO proxyGoodsOperationDTO) {
        goodsIntegrationService.editProxyGoods(proxyGoodsOperationDTO);
    }

    @Override
    public void syncGoodsSkuCommentCount(String skuId) {
        goodsIntegrationService.syncGoodsSkuCommentCount(skuId);
    }

    /**
     * 格式化商品为店铺所有
     *
     * @param goods 商品
     */
    private void storeOwnHandler(Goods goods) {

        String supplierGoodsId = goods.getId();
        String supplierId = goods.getStoreId();
        String supplierName = goods.getStoreName();

        AuthUser authUser = Objects.requireNonNull(UserContext.getCurrentUser());
        //格式化商品为店铺所有，需要将商品id置空，以及配置店铺id和店铺名称
        goods.setId(SnowFlake.getIdStr());
        goods.setStoreId(authUser.getExtendId());
        goods.setMarketEnable(GoodsMarketEnum.DOWN.name());
        goods.setStoreName(authUser.getExtendName());

        goods.setSupplierGoodsId(supplierGoodsId);
        goods.setSupplierId(supplierId);
        goods.setSupplierName(supplierName);
        goods.setSupplierEnum(
                SupplierEnum.getSupplierEnum(supplierId).name()
        );
        goods.setSupportProxy(false);
        goods.setSupportPurchase(false);
        goods.setSalesModel(SalesModeEnum.RETAIL.name());
        goods.setIsProxyGoods(true);

        // 写入当前用户场景
        SceneHelp.objectHandler(goods);

        //复制商品相册
        goodsGalleryService.copyGoodsGallery(supplierGoodsId, goods.getId());

    }

    /**
     * 格式化SKU为供应商所有
     *
     * @param goodsSkus 商品sku
     */
    private List<GoodsSku> storeOwnHandler(List<GoodsSku> goodsSkus, Goods goods) {
        AuthUser authUser = Objects.requireNonNull(UserContext.getCurrentUser());

        //接收sku集合
        List<GoodsSku> result = new ArrayList<>();
        //格式化商品为店铺所有，需要将商品id置空，以及配置店铺id和店铺名称
        goodsSkus.forEach(goodsSku -> {

            try {
                GoodsSku sku = goodsSku.clone();

                String supplierGoodsId = goodsSku.getGoodsId();
                String supplierSkuId = goodsSku.getId();
                String supplierId = goodsSku.getStoreId();
                String supplierName = goodsSku.getStoreName();

                sku.setId(null);
                sku.setGoodsId(goods.getId());
                sku.setStoreId(authUser.getExtendId());
                sku.setStoreName(authUser.getExtendName());
                sku.setMarketEnable(GoodsMarketEnum.DOWN.name());

                sku.setSupplierGoodsId(supplierGoodsId);
                sku.setSupplierSkuId(supplierSkuId);
                sku.setSupplierId(supplierId);
                sku.setSupplierName(supplierName);

                sku.setSupportProxy(false);
                sku.setSupportPurchase(false);
                sku.setSalesModel(SalesModeEnum.RETAIL.name());
                sku.setIsProxyGoods(true);
                sku.setFreightTemplateId(goods.getTemplateId());


                //默认代理商的价格为当前商品的价格的1.1倍
                sku.setPrice(CurrencyUtil.mul(sku.getPrice(), 1.1));

                //将代理商的价格设置为当前商品的成本价
                sku.setCost(goodsSku.getPrice());
                result.add(sku);
            } catch (Exception e) {
                throw new ServiceException(ResultCode.GOODS_SKU_SUPPLIER_ADD_ERROR);
            }
        });

        result.forEach(SceneHelp::objectHandler);
        return result;
    }

    @Override
    public GoodsTopDto goodsTop(String goodsId) {
        Goods goods = this.goodsService.getById(goodsId);
        if (goods == null || StringUtils.isBlank(goods.getCategoryPath())) {
            return null;
        }
        GoodsTopDto goodsTopDto = new GoodsTopDto();
        // 获取商品分类
        String[] categoryIds = goods.getCategoryPath().split(",");
        String categoryId = categoryIds[categoryIds.length - 1];
        if (GoodsType.VIP_CARDS.getCategoryId().equals(categoryId)) {
            goodsTopDto.setType(GoodsType.VIP_CARDS.getDescription());
        } else if (GoodsType.ENTREPRENEURIAL_GIFT_PACK.getCategoryId().equals(categoryId)) {
            goodsTopDto.setType(GoodsType.ENTREPRENEURIAL_GIFT_PACK.getDescription());
        } else if (GoodsType.AREA_GIFT_PACK.getCategoryId().equals(categoryId)) {
            goodsTopDto.setType(GoodsType.AREA_GIFT_PACK.getDescription());
            goodsTopDto.setIcon(true);
        } else if (GoodsType.STREET_GIFT_PACK.getCategoryId().equals(categoryId)) {
            goodsTopDto.setType(GoodsType.STREET_GIFT_PACK.getDescription());
            goodsTopDto.setIcon(true);
        } else if (GoodsType.VILLAGE_GIFT_PACK.getCategoryId().equals(categoryId)) {
            goodsTopDto.setType(GoodsType.VILLAGE_GIFT_PACK.getDescription());
            goodsTopDto.setIcon(true);
        } else {
            goodsTopDto.setType(GoodsType.GOODS.getDescription());
            goodsTopDto.setSplitRatio(goods.getSplitRatio());
            goodsTopDto.setIcon(true);
        }
        return goodsTopDto;
    }
}
