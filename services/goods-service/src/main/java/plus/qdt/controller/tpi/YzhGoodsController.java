package plus.qdt.controller.tpi;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import plus.qdt.common.utils.ResultUtil;
import plus.qdt.common.vo.ResultMessage;
import plus.qdt.modules.goods.entity.dos.GoodsSku;
import plus.qdt.modules.goods.entity.dto.GoodsDetailResult;
import plus.qdt.modules.goods.entity.dto.GoodsInitResult;
import plus.qdt.modules.goods.entity.dto.YZHGoodsSkuDTO;
import plus.qdt.modules.goods.entity.enums.SupplierEnum;
import plus.qdt.modules.goods.service.GoodsSkuService;
import plus.qdt.modules.goods.service.YzhGoodsService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 云中鹤三方api商品相关接口
 * <AUTHOR>
 * @since 2025-06-13
 */
@Slf4j
@RestController
@Tag(name = "云中鹤商品相关接口")
@RequestMapping("/goods/tpi/yzh")
@RequiredArgsConstructor
public class YzhGoodsController {

    private final YzhGoodsService yzhGoodsService;
    private final GoodsSkuService goodsSkuService;

    /**
     * 商品数据初始化
     * @param taskId 任务ID，用于更新进度
     * @param userId 用户ID
     * @param scene 场景
     * @param extendId 扩展ID
     * @return ResultMessage<Object> 初始化结果
     * <AUTHOR>
     */
    @GetMapping("/goodsInit")
    @Operation(summary = "商品数据初始化", description = "从云中鹤平台获取商品数据并初始化到本地系统")
    public ResultMessage<Object> goodsInit(@RequestParam(required = false) String taskId,
                                          @RequestParam(required = false) String userId,
                                          @RequestParam(required = false) String scene,
                                          @RequestParam(required = false) String extendId) {
        try {
            log.info("开始云中鹤商品初始化，任务ID: {}, 用户ID: {}, 场景: {}, 扩展ID: {}", taskId, userId, scene, extendId);

            // 执行商品数据初始化（获取商品数据并保存）
            GoodsInitResult result = yzhGoodsService.executeGoodsInitializationWithProgress(taskId, userId, scene, extendId);

            if (result.isEmpty()) {
                return ResultUtil.error("商品数据初始化失败，未获取到任何商品数据");
            }

            log.info("云中鹤商品初始化完成，共处理 {} 条商品数据", result.getGoodsCount());
            return ResultUtil.success();

        } catch (Exception e) {
            return ResultUtil.error("商品数据初始化失败: " + e.getMessage());
        }
    }

    /**
     * 检查商品数据分类
     * @param skuCodes SKU编码列表，用逗号分隔
     * @return ResultMessage<Object> 检查结果
     * <AUTHOR>
     */
    @GetMapping("/checkDuplicate")
    @Operation(summary = "检查商品数据分类", description = "根据云中鹤SKU编码检查商品是新增还是更新（使用supplierGoodsId字段比对）")
    public ResultMessage<Object> checkDuplicate(@RequestParam String skuCodes) {
        try {
            if (skuCodes == null || skuCodes.trim().isEmpty()) {
                return ResultUtil.error("SKU编码不能为空");
            }

            // 解析SKU编码列表
            List<String> skuCodeList = List.of(skuCodes.split(","))
                    .stream()
                    .map(String::trim)
                    .filter(code -> !code.isEmpty())
                    .collect(Collectors.toList());

            if (skuCodeList.isEmpty()) {
                return ResultUtil.error("没有有效的SKU编码");
            }

            log.info("开始检查商品数据分类，SKU数量: {}", skuCodeList.size());

            // 调用服务层的重复性检查方法
            java.util.Set<String> existingSkuSet = yzhGoodsService.getExistingSkuCodes(skuCodeList);

            Map<String, Object> result = new HashMap<>();
            List<String> updateSkus = new ArrayList<>(existingSkuSet);
            List<String> newSkus = skuCodeList.stream()
                    .filter(sku -> !existingSkuSet.contains(sku))
                    .collect(Collectors.toList());

            result.put("totalCount", skuCodeList.size());
            result.put("updateCount", updateSkus.size());
            result.put("newCount", newSkus.size());
            result.put("updateSkus", updateSkus);
            result.put("newSkus", newSkus);
            result.put("operation", Map.of(
                "newSkus", "将执行新增操作",
                "updateSkus", "将执行更新操作"
            ));

            log.info("商品数据分类完成，总数: {}, 更新: {}, 新增: {}",
                    skuCodeList.size(), updateSkus.size(), newSkus.size());

            return ResultUtil.data(result);

        } catch (Exception e) {
            log.error("检查商品数据分类失败: {}", e.getMessage(), e);
            return ResultUtil.error("检查商品数据分类失败: " + e.getMessage());
        }
    }

    /**
     * 检查单个SKU是否存在
     * 使用 supplierGoodsId 字段进行比对
     */
    private boolean checkSkuExists(String skuCode) {
        try {
            if (skuCode == null || skuCode.trim().isEmpty()) {
                return false;
            }

            long count = goodsSkuService.count(
                    new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<GoodsSku>()
                            .eq(GoodsSku::getSupplierGoodsId, skuCode)
                            .eq(GoodsSku::getSupplierId, SupplierEnum.YZH.getDefaultId())
                            .eq(GoodsSku::getDeleteFlag, false)
            );

            return count > 0;

        } catch (Exception e) {
            log.error("检查SKU是否存在时发生异常，SKU: {}, 异常: {}", skuCode, e.getMessage(), e);
            return false;
        }
    }

}
